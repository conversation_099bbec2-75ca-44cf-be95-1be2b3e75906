from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from .models import CustomUser, UserRole, UserSession, UserActivity


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'can_view', 'can_add', 'can_edit', 'can_delete']
    list_filter = ['can_view', 'can_add', 'can_edit', 'can_delete', 'can_approve']
    search_fields = ['name', 'description']
    fieldsets = (
        (None, {
            'fields': ('name', 'description')
        }),
        ('الصلاحيات الأساسية', {
            'fields': ('can_view', 'can_add', 'can_edit', 'can_delete')
        }),
        ('صلاحيات متقدمة', {
            'fields': ('can_approve', 'can_transfer', 'can_generate_reports', 'can_manage_users', 'can_backup')
        }),
    )


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = ['username', 'arabic_name', 'email', 'department', 'role', 'is_active', 'is_staff']
    list_filter = ['is_active', 'is_staff', 'is_superuser', 'department', 'role', 'is_active_employee']
    search_fields = ['username', 'arabic_name', 'email', 'employee_id', 'first_name', 'last_name']
    ordering = ['username']

    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name', 'arabic_name', 'email')}),
        ('معلومات الموظف', {'fields': ('employee_id', 'phone', 'position', 'department', 'role', 'hire_date')}),
        (_('Permissions'), {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'is_active_employee', 'groups', 'user_permissions'),
        }),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
        ('معلومات الأمان', {'fields': ('last_login_ip', 'failed_login_attempts', 'account_locked_until')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'arabic_name', 'employee_id', 'email', 'password1', 'password2', 'department', 'role'),
        }),
    )


@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    list_display = ['user', 'ip_address', 'login_time', 'logout_time', 'is_active']
    list_filter = ['is_active', 'login_time', 'logout_time']
    search_fields = ['user__username', 'user__arabic_name', 'ip_address']
    readonly_fields = ['session_key', 'user_agent', 'login_time', 'logout_time']
    ordering = ['-login_time']


@admin.register(UserActivity)
class UserActivityAdmin(admin.ModelAdmin):
    list_display = ['user', 'activity_type', 'description', 'ip_address', 'timestamp']
    list_filter = ['activity_type', 'timestamp']
    search_fields = ['user__username', 'user__arabic_name', 'activity_type', 'description']
    readonly_fields = ['user', 'activity_type', 'description', 'ip_address', 'timestamp']
    ordering = ['-timestamp']

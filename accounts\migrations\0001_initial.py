# Generated by Django 5.2.4 on 2025-07-07 17:35

import django.contrib.auth.models
import django.contrib.auth.validators
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('arabic_name', models.CharField(max_length=100, verbose_name='الاسم بالعربية')),
                ('employee_id', models.CharField(max_length=20, unique=True, verbose_name='رقم الموظف')),
                ('phone', models.CharField(blank=True, max_length=15, verbose_name='رقم الهاتف')),
                ('position', models.CharField(blank=True, max_length=100, verbose_name='المنصب')),
                ('is_active_employee', models.BooleanField(default=True, verbose_name='موظف نشط')),
                ('hire_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التعيين')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='آخر IP')),
                ('failed_login_attempts', models.IntegerField(default=0, verbose_name='محاولات الدخول الفاشلة')),
                ('account_locked_until', models.DateTimeField(blank=True, null=True, verbose_name='مقفل حتى')),
            ],
            options={
                'verbose_name': 'مستخدم',
                'verbose_name_plural': 'المستخدمون',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='UserActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(max_length=50, verbose_name='نوع النشاط')),
                ('description', models.TextField(verbose_name='وصف النشاط')),
                ('ip_address', models.GenericIPAddressField(verbose_name='عنوان IP')),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now, verbose_name='الوقت')),
            ],
            options={
                'verbose_name': 'نشاط مستخدم',
                'verbose_name_plural': 'أنشطة المستخدمين',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='اسم الدور')),
                ('description', models.TextField(blank=True, verbose_name='وصف الدور')),
                ('can_view', models.BooleanField(default=True, verbose_name='يمكن المشاهدة')),
                ('can_add', models.BooleanField(default=False, verbose_name='يمكن الإضافة')),
                ('can_edit', models.BooleanField(default=False, verbose_name='يمكن التعديل')),
                ('can_delete', models.BooleanField(default=False, verbose_name='يمكن الحذف')),
                ('can_approve', models.BooleanField(default=False, verbose_name='يمكن الموافقة')),
                ('can_transfer', models.BooleanField(default=False, verbose_name='يمكن التحويل')),
                ('can_generate_reports', models.BooleanField(default=False, verbose_name='يمكن إنشاء التقارير')),
                ('can_manage_users', models.BooleanField(default=False, verbose_name='يمكن إدارة المستخدمين')),
                ('can_backup', models.BooleanField(default=False, verbose_name='يمكن النسخ الاحتياطي')),
            ],
            options={
                'verbose_name': 'دور مستخدم',
                'verbose_name_plural': 'أدوار المستخدمين',
            },
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(max_length=40, verbose_name='مفتاح الجلسة')),
                ('ip_address', models.GenericIPAddressField(verbose_name='عنوان IP')),
                ('user_agent', models.TextField(verbose_name='معلومات المتصفح')),
                ('login_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='وقت الدخول')),
                ('logout_time', models.DateTimeField(blank=True, null=True, verbose_name='وقت الخروج')),
                ('is_active', models.BooleanField(default=True, verbose_name='جلسة نشطة')),
            ],
            options={
                'verbose_name': 'جلسة مستخدم',
                'verbose_name_plural': 'جلسات المستخدمين',
                'ordering': ['-login_time'],
            },
        ),
    ]

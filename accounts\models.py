from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone


class UserRole(models.Model):
    """أدوار المستخدمين"""
    name = models.CharField(max_length=50, unique=True, verbose_name="اسم الدور")
    description = models.TextField(blank=True, verbose_name="وصف الدور")

    # الصلاحيات
    can_view = models.BooleanField(default=True, verbose_name="يمكن المشاهدة")
    can_add = models.BooleanField(default=False, verbose_name="يمكن الإضافة")
    can_edit = models.BooleanField(default=False, verbose_name="يمكن التعديل")
    can_delete = models.BooleanField(default=False, verbose_name="يمكن الحذف")
    can_approve = models.BooleanField(default=False, verbose_name="يمكن الموافقة")
    can_transfer = models.BooleanField(default=False, verbose_name="يمكن التحويل")
    can_generate_reports = models.BooleanField(default=False, verbose_name="يمكن إنشاء التقارير")
    can_manage_users = models.BooleanField(default=False, verbose_name="يمكن إدارة المستخدمين")
    can_backup = models.BooleanField(default=False, verbose_name="يمكن النسخ الاحتياطي")

    class Meta:
        verbose_name = "دور مستخدم"
        verbose_name_plural = "أدوار المستخدمين"

    def __str__(self):
        return self.name


class CustomUser(AbstractUser):
    """نموذج المستخدم المخصص"""
    # معلومات شخصية إضافية
    arabic_name = models.CharField(max_length=100, verbose_name="الاسم بالعربية")
    employee_id = models.CharField(max_length=20, unique=True, verbose_name="رقم الموظف")
    phone = models.CharField(max_length=15, blank=True, verbose_name="رقم الهاتف")
    position = models.CharField(max_length=100, blank=True, verbose_name="المنصب")

    # الانتماء التنظيمي
    department = models.ForeignKey('documents.Department', on_delete=models.SET_NULL,
                                 null=True, blank=True, verbose_name="القسم")
    role = models.ForeignKey(UserRole, on_delete=models.SET_NULL,
                           null=True, blank=True, verbose_name="الدور")

    # إعدادات الحساب
    is_active_employee = models.BooleanField(default=True, verbose_name="موظف نشط")
    hire_date = models.DateField(null=True, blank=True, verbose_name="تاريخ التعيين")

    # إعدادات النظام
    last_login_ip = models.GenericIPAddressField(null=True, blank=True, verbose_name="آخر IP")
    failed_login_attempts = models.IntegerField(default=0, verbose_name="محاولات الدخول الفاشلة")
    account_locked_until = models.DateTimeField(null=True, blank=True, verbose_name="مقفل حتى")

    class Meta:
        verbose_name = "مستخدم"
        verbose_name_plural = "المستخدمون"

    def __str__(self):
        return f"{self.arabic_name} ({self.username})"

    def get_full_arabic_name(self):
        return self.arabic_name or f"{self.first_name} {self.last_name}"

    def has_permission(self, permission):
        """فحص الصلاحيات"""
        if self.is_superuser:
            return True
        if not self.role:
            return False
        return getattr(self.role, f'can_{permission}', False)

    def is_account_locked(self):
        """فحص إذا كان الحساب مقفل"""
        if self.account_locked_until:
            return timezone.now() < self.account_locked_until
        return False


class UserSession(models.Model):
    """جلسات المستخدمين"""
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, verbose_name="المستخدم")
    session_key = models.CharField(max_length=40, verbose_name="مفتاح الجلسة")
    ip_address = models.GenericIPAddressField(verbose_name="عنوان IP")
    user_agent = models.TextField(verbose_name="معلومات المتصفح")
    login_time = models.DateTimeField(default=timezone.now, verbose_name="وقت الدخول")
    logout_time = models.DateTimeField(null=True, blank=True, verbose_name="وقت الخروج")
    is_active = models.BooleanField(default=True, verbose_name="جلسة نشطة")

    class Meta:
        verbose_name = "جلسة مستخدم"
        verbose_name_plural = "جلسات المستخدمين"
        ordering = ['-login_time']

    def __str__(self):
        return f"{self.user.arabic_name} - {self.login_time}"


class UserActivity(models.Model):
    """سجل أنشطة المستخدمين"""
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, verbose_name="المستخدم")
    activity_type = models.CharField(max_length=50, verbose_name="نوع النشاط")
    description = models.TextField(verbose_name="وصف النشاط")
    ip_address = models.GenericIPAddressField(verbose_name="عنوان IP")
    timestamp = models.DateTimeField(default=timezone.now, verbose_name="الوقت")

    class Meta:
        verbose_name = "نشاط مستخدم"
        verbose_name_plural = "أنشطة المستخدمين"
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.user.arabic_name} - {self.activity_type}"

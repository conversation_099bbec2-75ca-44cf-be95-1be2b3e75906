from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.forms import AuthenticationForm
from .models import UserSession, UserActivity
import json


def get_client_ip(request):
    """الحصول على عنوان IP الخاص بالعميل"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def log_user_activity(user, activity_type, description, request):
    """تسجيل نشاط المستخدم"""
    UserActivity.objects.create(
        user=user,
        activity_type=activity_type,
        description=description,
        ip_address=get_client_ip(request)
    )


def custom_login_view(request):
    """صفحة تسجيل الدخول المخصصة"""
    if request.user.is_authenticated:
        return redirect('dashboard')

    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)

            if user is not None:
                # فحص إذا كان الحساب مقفل
                if user.is_account_locked():
                    messages.error(request, 'حسابك مقفل مؤقتاً. يرجى المحاولة لاحقاً.')
                    return render(request, 'accounts/login.html', {'form': form})

                # تسجيل الدخول
                login(request, user)

                # إعادة تعيين محاولات الدخول الفاشلة
                user.failed_login_attempts = 0
                user.last_login_ip = get_client_ip(request)
                user.save()

                # إنشاء جلسة مستخدم
                UserSession.objects.create(
                    user=user,
                    session_key=request.session.session_key,
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )

                # تسجيل النشاط
                log_user_activity(user, 'login', 'تسجيل دخول ناجح', request)

                messages.success(request, f'مرحباً {user.get_full_arabic_name()}')

                # إعادة التوجيه إلى الصفحة المطلوبة أو الرئيسية
                next_url = request.GET.get('next', 'dashboard')
                return redirect(next_url)
            else:
                # زيادة عدد محاولات الدخول الفاشلة
                try:
                    user = User.objects.get(username=username)
                    user.failed_login_attempts += 1
                    if user.failed_login_attempts >= 5:
                        user.account_locked_until = timezone.now() + timezone.timedelta(minutes=30)
                    user.save()

                    log_user_activity(user, 'failed_login', 'محاولة دخول فاشلة', request)
                except User.DoesNotExist:
                    pass

                messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة.')
        else:
            messages.error(request, 'يرجى تصحيح الأخطاء أدناه.')
    else:
        form = AuthenticationForm()

    return render(request, 'accounts/login.html', {'form': form})


@login_required
def custom_logout_view(request):
    """تسجيل الخروج المخصص"""
    # تحديث جلسة المستخدم
    try:
        session = UserSession.objects.get(
            user=request.user,
            session_key=request.session.session_key,
            is_active=True
        )
        session.logout_time = timezone.now()
        session.is_active = False
        session.save()
    except UserSession.DoesNotExist:
        pass

    # تسجيل النشاط
    log_user_activity(request.user, 'logout', 'تسجيل خروج', request)

    logout(request)
    messages.success(request, 'تم تسجيل الخروج بنجاح.')
    return redirect('login')


@login_required
def dashboard_view(request):
    """الصفحة الرئيسية"""
    context = {
        'user': request.user,
        'recent_activities': UserActivity.objects.filter(user=request.user)[:10],
    }
    return render(request, 'dashboard.html', context)


@login_required
def profile_view(request):
    """صفحة الملف الشخصي"""
    context = {
        'user': request.user,
        'sessions': UserSession.objects.filter(user=request.user)[:10],
        'activities': UserActivity.objects.filter(user=request.user)[:20],
    }
    return render(request, 'accounts/profile.html', context)


@csrf_exempt
@login_required
def check_permissions(request):
    """فحص صلاحيات المستخدم عبر AJAX"""
    if request.method == 'POST':
        data = json.loads(request.body)
        permission = data.get('permission')

        has_permission = request.user.has_permission(permission)

        return JsonResponse({
            'has_permission': has_permission,
            'user_role': request.user.role.name if request.user.role else None
        })

    return JsonResponse({'error': 'Invalid request'}, status=400)

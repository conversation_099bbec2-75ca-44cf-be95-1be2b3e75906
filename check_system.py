#!/usr/bin/env python
"""
سكريبت فحص شامل لوظائف النظام
"""
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inout_system.settings')
django.setup()

from django.contrib.auth import get_user_model
from documents.models import *
from accounts.models import *

User = get_user_model()

def check_database():
    """فحص قاعدة البيانات"""
    print('=== إحصائيات قاعدة البيانات ===')
    print(f'المستخدمون: {User.objects.count()}')
    print(f'الأقسام: {Department.objects.count()}')
    print(f'أنواع الكتب: {DocumentType.objects.count()}')
    print(f'حالات الكتب: {DocumentStatus.objects.count()}')
    print(f'الكتب الواردة: {IncomingDocument.objects.count()}')
    print(f'الكتب الصادرة: {OutgoingDocument.objects.count()}')
    print(f'التحويلات: {DocumentTransfer.objects.count()}')
    print(f'الأدوار: {UserRole.objects.count()}')

def check_users():
    """فحص المستخدمين"""
    print('\n=== المستخدمون ===')
    for user in User.objects.all():
        role_name = user.role.name if user.role else "بدون دور"
        dept_name = user.department.name if user.department else "بدون قسم"
        print(f'- {user.arabic_name} ({user.username}) - {role_name} - {dept_name}')

def check_documents():
    """فحص الكتب"""
    print('\n=== الكتب الواردة ===')
    for doc in IncomingDocument.objects.all():
        print(f'- {doc.incoming_number}: {doc.subject[:50]}... - {doc.status.name}')
    
    print('\n=== الكتب الصادرة ===')
    for doc in OutgoingDocument.objects.all():
        print(f'- {doc.document_number}: {doc.subject[:50]}... - {doc.status.name}')

def check_permissions():
    """فحص الصلاحيات"""
    print('\n=== الأدوار والصلاحيات ===')
    for role in UserRole.objects.all():
        permissions = []
        if role.can_view: permissions.append('مشاهدة')
        if role.can_add: permissions.append('إضافة')
        if role.can_edit: permissions.append('تعديل')
        if role.can_delete: permissions.append('حذف')
        if role.can_approve: permissions.append('موافقة')
        if role.can_transfer: permissions.append('تحويل')
        if role.can_generate_reports: permissions.append('تقارير')
        if role.can_manage_users: permissions.append('إدارة مستخدمين')
        if role.can_backup: permissions.append('نسخ احتياطي')
        
        print(f'- {role.name}: {", ".join(permissions)}')

def check_departments():
    """فحص الأقسام"""
    print('\n=== الأقسام ===')
    for dept in Department.objects.all():
        head_name = dept.head.arabic_name if dept.head else "بدون رئيس"
        user_count = User.objects.filter(department=dept).count()
        print(f'- {dept.name} ({dept.code}) - رئيس: {head_name} - الموظفون: {user_count}')

def check_document_types_and_statuses():
    """فحص أنواع وحالات الكتب"""
    print('\n=== أنواع الكتب ===')
    for doc_type in DocumentType.objects.all():
        incoming_count = IncomingDocument.objects.filter(document_type=doc_type).count()
        outgoing_count = OutgoingDocument.objects.filter(document_type=doc_type).count()
        print(f'- {doc_type.name} ({doc_type.code}) - واردة: {incoming_count}, صادرة: {outgoing_count}')
    
    print('\n=== حالات الكتب ===')
    for status in DocumentStatus.objects.all():
        incoming_count = IncomingDocument.objects.filter(status=status).count()
        outgoing_count = OutgoingDocument.objects.filter(status=status).count()
        print(f'- {status.name} - واردة: {incoming_count}, صادرة: {outgoing_count}')

def check_transfers():
    """فحص التحويلات"""
    print('\n=== التحويلات الداخلية ===')
    for transfer in DocumentTransfer.objects.all():
        status = "تم الاستلام" if transfer.is_received else "معلق"
        print(f'- {transfer.incoming_document.incoming_number}: {transfer.from_department.name} → {transfer.to_department.name} - {status}')

def check_actions():
    """فحص سجل الأحداث"""
    print('\n=== سجل الأحداث (آخر 10) ===')
    for action in DocumentAction.objects.all()[:10]:
        doc_ref = action.incoming_document or action.outgoing_document
        print(f'- {action.get_action_type_display()}: {doc_ref} - {action.performed_by.arabic_name} - {action.performed_at.strftime("%Y-%m-%d %H:%M")}')

def main():
    """تشغيل جميع الفحوصات"""
    print("🔍 فحص شامل لنظام إدارة الصادرة والواردة")
    print("=" * 50)
    
    try:
        check_database()
        check_users()
        check_departments()
        check_document_types_and_statuses()
        check_documents()
        check_transfers()
        check_permissions()
        check_actions()
        
        print("\n✅ تم الانتهاء من الفحص بنجاح!")
        
    except Exception as e:
        print(f"\n❌ خطأ أثناء الفحص: {e}")

if __name__ == '__main__':
    main()

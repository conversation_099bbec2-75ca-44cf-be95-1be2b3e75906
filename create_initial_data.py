#!/usr/bin/env python
"""
سكريبت لإنشاء البيانات الأولية للنظام
"""
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inout_system.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import UserRole
from documents.models import Department, DocumentType, DocumentStatus

User = get_user_model()

def create_user_roles():
    """إنشاء أدوار المستخدمين"""
    roles = [
        {
            'name': 'مدير النظام',
            'description': 'صلاحيات كاملة لإدارة النظام',
            'can_view': True,
            'can_add': True,
            'can_edit': True,
            'can_delete': True,
            'can_approve': True,
            'can_transfer': True,
            'can_generate_reports': True,
            'can_manage_users': True,
            'can_backup': True,
        },
        {
            'name': 'مدير قسم',
            'description': 'إدارة الكتب والموظفين في القسم',
            'can_view': True,
            'can_add': True,
            'can_edit': True,
            'can_delete': False,
            'can_approve': True,
            'can_transfer': True,
            'can_generate_reports': True,
            'can_manage_users': False,
            'can_backup': False,
        },
        {
            'name': 'موظف',
            'description': 'إدخال ومعالجة الكتب',
            'can_view': True,
            'can_add': True,
            'can_edit': True,
            'can_delete': False,
            'can_approve': False,
            'can_transfer': True,
            'can_generate_reports': False,
            'can_manage_users': False,
            'can_backup': False,
        },
        {
            'name': 'مراقب',
            'description': 'مراقبة ومتابعة الكتب',
            'can_view': True,
            'can_add': False,
            'can_edit': False,
            'can_delete': False,
            'can_approve': False,
            'can_transfer': False,
            'can_generate_reports': True,
            'can_manage_users': False,
            'can_backup': False,
        },
        {
            'name': 'قراءة فقط',
            'description': 'مشاهدة الكتب فقط',
            'can_view': True,
            'can_add': False,
            'can_edit': False,
            'can_delete': False,
            'can_approve': False,
            'can_transfer': False,
            'can_generate_reports': False,
            'can_manage_users': False,
            'can_backup': False,
        },
    ]
    
    for role_data in roles:
        role, created = UserRole.objects.get_or_create(
            name=role_data['name'],
            defaults=role_data
        )
        if created:
            print(f"تم إنشاء دور: {role.name}")
        else:
            print(f"الدور موجود مسبقاً: {role.name}")

def create_departments():
    """إنشاء الأقسام"""
    departments = [
        {'name': 'الإدارة العامة', 'code': 'ADM'},
        {'name': 'الشؤون الإدارية', 'code': 'HR'},
        {'name': 'الشؤون المالية', 'code': 'FIN'},
        {'name': 'الشؤون القانونية', 'code': 'LEG'},
        {'name': 'تكنولوجيا المعلومات', 'code': 'IT'},
        {'name': 'العلاقات العامة', 'code': 'PR'},
    ]
    
    for dept_data in departments:
        dept, created = Department.objects.get_or_create(
            code=dept_data['code'],
            defaults=dept_data
        )
        if created:
            print(f"تم إنشاء قسم: {dept.name}")
        else:
            print(f"القسم موجود مسبقاً: {dept.name}")

def create_document_types():
    """إنشاء أنواع الكتب"""
    doc_types = [
        {'name': 'كتاب رسمي', 'code': 'OFF'},
        {'name': 'أمر إداري', 'code': 'ADO'},
        {'name': 'تعميم', 'code': 'CIR'},
        {'name': 'طلب', 'code': 'REQ'},
        {'name': 'توصية', 'code': 'REC'},
        {'name': 'تقرير', 'code': 'REP'},
        {'name': 'استفسار', 'code': 'INQ'},
        {'name': 'شكوى', 'code': 'COM'},
    ]
    
    for type_data in doc_types:
        doc_type, created = DocumentType.objects.get_or_create(
            code=type_data['code'],
            defaults=type_data
        )
        if created:
            print(f"تم إنشاء نوع كتاب: {doc_type.name}")
        else:
            print(f"نوع الكتاب موجود مسبقاً: {doc_type.name}")

def create_document_statuses():
    """إنشاء حالات الكتب"""
    statuses = [
        {'name': 'جديد', 'color': '#007bff'},
        {'name': 'قيد المراجعة', 'color': '#ffc107'},
        {'name': 'قيد الإنجاز', 'color': '#fd7e14'},
        {'name': 'منجز', 'color': '#28a745'},
        {'name': 'مؤجل', 'color': '#6c757d'},
        {'name': 'مرفوض', 'color': '#dc3545'},
        {'name': 'مؤرشف', 'color': '#6f42c1'},
    ]
    
    for status_data in statuses:
        status, created = DocumentStatus.objects.get_or_create(
            name=status_data['name'],
            defaults=status_data
        )
        if created:
            print(f"تم إنشاء حالة: {status.name}")
        else:
            print(f"الحالة موجودة مسبقاً: {status.name}")

def create_admin_user():
    """إنشاء مستخدم إداري"""
    admin_role = UserRole.objects.get(name='مدير النظام')
    admin_dept = Department.objects.get(code='ADM')
    
    if not User.objects.filter(username='admin').exists():
        admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            arabic_name='مدير النظام',
            employee_id='EMP001',
            department=admin_dept,
            role=admin_role,
            is_staff=True,
            is_superuser=True
        )
        print(f"تم إنشاء المستخدم الإداري: {admin_user.username}")
    else:
        print("المستخدم الإداري موجود مسبقاً")

def main():
    """تشغيل جميع العمليات"""
    print("بدء إنشاء البيانات الأولية...")
    
    create_user_roles()
    create_departments()
    create_document_types()
    create_document_statuses()
    create_admin_user()
    
    print("تم الانتهاء من إنشاء البيانات الأولية!")

if __name__ == '__main__':
    main()

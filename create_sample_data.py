#!/usr/bin/env python
"""
سكريبت لإنشاء بيانات تجريبية للنظام
"""
import os
import sys
import django
from datetime import datetime, timedelta
from django.utils import timezone

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inout_system.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import UserRole
from documents.models import (
    Department, DocumentType, DocumentStatus, 
    IncomingDocument, OutgoingDocument, DocumentTransfer,
    DocumentAction, DocumentComment
)

User = get_user_model()

def create_sample_users():
    """إنشاء مستخدمين تجريبيين"""
    print("إنشاء مستخدمين تجريبيين...")
    
    # الحصول على الأدوار والأقسام
    admin_role = UserRole.objects.get(name='مدير النظام')
    manager_role = UserRole.objects.get(name='مدير قسم')
    employee_role = UserRole.objects.get(name='موظف')
    
    admin_dept = Department.objects.get(code='ADM')
    hr_dept = Department.objects.get(code='HR')
    fin_dept = Department.objects.get(code='FIN')
    it_dept = Department.objects.get(code='IT')
    
    users_data = [
        {
            'username': 'ahmed.manager',
            'password': 'password123',
            'arabic_name': 'أحمد محمد علي',
            'employee_id': 'EMP002',
            'email': '<EMAIL>',
            'phone': '07901234567',
            'position': 'مدير الشؤون الإدارية',
            'department': hr_dept,
            'role': manager_role,
            'is_staff': True,
        },
        {
            'username': 'fatima.employee',
            'password': 'password123',
            'arabic_name': 'فاطمة حسن محمود',
            'employee_id': 'EMP003',
            'email': '<EMAIL>',
            'phone': '07801234567',
            'position': 'موظفة إدارية',
            'department': hr_dept,
            'role': employee_role,
        },
        {
            'username': 'omar.finance',
            'password': 'password123',
            'arabic_name': 'عمر خالد أحمد',
            'employee_id': 'EMP004',
            'email': '<EMAIL>',
            'phone': '07701234567',
            'position': 'مدير الشؤون المالية',
            'department': fin_dept,
            'role': manager_role,
            'is_staff': True,
        },
        {
            'username': 'sara.it',
            'password': 'password123',
            'arabic_name': 'سارة عبدالله يوسف',
            'employee_id': 'EMP005',
            'email': '<EMAIL>',
            'phone': '07601234567',
            'position': 'مطورة نظم',
            'department': it_dept,
            'role': employee_role,
        },
    ]
    
    for user_data in users_data:
        if not User.objects.filter(username=user_data['username']).exists():
            user = User.objects.create_user(**user_data)
            print(f"تم إنشاء المستخدم: {user.arabic_name}")
        else:
            print(f"المستخدم موجود مسبقاً: {user_data['username']}")

def create_sample_documents():
    """إنشاء كتب تجريبية"""
    print("إنشاء كتب تجريبية...")
    
    # الحصول على البيانات المطلوبة
    users = User.objects.all()
    departments = Department.objects.all()
    doc_types = DocumentType.objects.all()
    statuses = DocumentStatus.objects.all()
    
    # كتب واردة تجريبية
    incoming_docs_data = [
        {
            'document_number': 'م.خ/2025/001',
            'document_date': timezone.now().date() - timedelta(days=5),
            'sender_entity': 'وزارة التربية',
            'subject': 'طلب تقرير عن أداء الموظفين للربع الأول من عام 2025',
            'incoming_number': 'و/2025/001',
            'document_type': doc_types.get(code='REQ'),
            'status': statuses.get(name='قيد المراجعة'),
            'priority': 'high',
            'assigned_to_department': departments.get(code='HR'),
            'received_by': users.get(username='ahmed.manager'),
            'created_by': users.get(username='admin'),
            'notes': 'يرجى إعداد التقرير خلال أسبوع من تاريخ الاستلام',
            'deadline': timezone.now().date() + timedelta(days=2),
        },
        {
            'document_number': 'م.م/2025/015',
            'document_date': timezone.now().date() - timedelta(days=3),
            'sender_entity': 'وزارة المالية',
            'subject': 'تعليمات جديدة بخصوص الموازنة السنوية لعام 2025',
            'incoming_number': 'و/2025/002',
            'document_type': doc_types.get(code='CIR'),
            'status': statuses.get(name='منجز'),
            'priority': 'medium',
            'assigned_to_department': departments.get(code='FIN'),
            'received_by': users.get(username='omar.finance'),
            'created_by': users.get(username='admin'),
            'notes': 'تم تطبيق التعليمات الجديدة',
        },
        {
            'document_number': 'ت.م/2025/008',
            'document_date': timezone.now().date() - timedelta(days=1),
            'sender_entity': 'ديوان الرقابة المالية',
            'subject': 'استفسار حول المصروفات الإدارية للشهر الماضي',
            'incoming_number': 'و/2025/003',
            'document_type': doc_types.get(code='INQ'),
            'status': statuses.get(name='جديد'),
            'priority': 'urgent',
            'assigned_to_department': departments.get(code='FIN'),
            'received_by': users.get(username='fatima.employee'),
            'created_by': users.get(username='admin'),
            'deadline': timezone.now().date() + timedelta(days=1),
        },
    ]
    
    for doc_data in incoming_docs_data:
        if not IncomingDocument.objects.filter(incoming_number=doc_data['incoming_number']).exists():
            doc = IncomingDocument.objects.create(**doc_data)
            print(f"تم إنشاء كتاب وارد: {doc.incoming_number}")
            
            # إضافة إجراء
            DocumentAction.objects.create(
                incoming_document=doc,
                action_type='create',
                description=f'تم إدخال الكتاب الوارد رقم {doc.incoming_number}',
                performed_by=doc.created_by
            )
        else:
            print(f"الكتاب الوارد موجود مسبقاً: {doc_data['incoming_number']}")
    
    # كتب صادرة تجريبية
    outgoing_docs_data = [
        {
            'recipient_entity': 'وزارة التعليم العالي',
            'subject': 'طلب تعاون في مجال التدريب',
            'content': 'نتشرف بأن نطلب من سيادتكم التعاون معنا في مجال تدريب الموظفين وتطوير قدراتهم الإدارية والفنية.',
            'document_type': doc_types.get(code='REQ'),
            'department': departments.get(code='HR'),
            'status': statuses.get(name='منجز'),
            'priority': 'medium',
            'prepared_by': users.get(username='ahmed.manager'),
            'approved_by': users.get(username='admin'),
            'delivery_method': 'hand',
            'sent_date': timezone.now() - timedelta(days=2),
        },
        {
            'recipient_entity': 'مجلس الوزراء',
            'subject': 'تقرير شهري عن أنشطة الدائرة',
            'content': 'نتشرف بأن نرفع إليكم التقرير الشهري عن أنشطة دائرتنا للشهر المنصرم، متضمناً الإنجازات والتحديات.',
            'document_type': doc_types.get(code='REP'),
            'department': departments.get(code='ADM'),
            'status': statuses.get(name='قيد المراجعة'),
            'priority': 'high',
            'prepared_by': users.get(username='fatima.employee'),
            'delivery_method': 'email',
        },
    ]
    
    for doc_data in outgoing_docs_data:
        doc = OutgoingDocument.objects.create(**doc_data)
        print(f"تم إنشاء كتاب صادر: {doc.document_number}")
        
        # إضافة إجراء
        DocumentAction.objects.create(
            outgoing_document=doc,
            action_type='create',
            description=f'تم إنشاء الكتاب الصادر رقم {doc.document_number}',
            performed_by=doc.prepared_by
        )

def create_sample_transfers():
    """إنشاء تحويلات تجريبية"""
    print("إنشاء تحويلات تجريبية...")
    
    # الحصول على كتاب وارد للتحويل
    incoming_doc = IncomingDocument.objects.filter(incoming_number='و/2025/001').first()
    if incoming_doc:
        transfer = DocumentTransfer.objects.create(
            incoming_document=incoming_doc,
            from_department=Department.objects.get(code='ADM'),
            to_department=Department.objects.get(code='HR'),
            from_user=User.objects.get(username='admin'),
            to_user=User.objects.get(username='ahmed.manager'),
            notes='يرجى المراجعة والرد خلال 3 أيام',
            is_received=True,
            received_date=timezone.now() - timedelta(hours=2),
            received_by=User.objects.get(username='ahmed.manager')
        )
        print(f"تم إنشاء تحويل: {transfer}")

def create_sample_comments():
    """إنشاء تعليقات تجريبية"""
    print("إنشاء تعليقات تجريبية...")
    
    incoming_doc = IncomingDocument.objects.filter(incoming_number='و/2025/001').first()
    if incoming_doc:
        DocumentComment.objects.create(
            incoming_document=incoming_doc,
            comment='تم مراجعة الطلب وسيتم إعداد التقرير المطلوب',
            commented_by=User.objects.get(username='ahmed.manager'),
            is_internal=True
        )
        print("تم إنشاء تعليق على الكتاب الوارد")

def main():
    """تشغيل جميع العمليات"""
    print("بدء إنشاء البيانات التجريبية...")
    
    create_sample_users()
    create_sample_documents()
    create_sample_transfers()
    create_sample_comments()
    
    print("تم الانتهاء من إنشاء البيانات التجريبية!")
    print("\nيمكنك الآن تسجيل الدخول باستخدام:")
    print("- admin / admin123 (مدير النظام)")
    print("- ahmed.manager / password123 (مدير قسم)")
    print("- fatima.employee / password123 (موظفة)")

if __name__ == '__main__':
    main()

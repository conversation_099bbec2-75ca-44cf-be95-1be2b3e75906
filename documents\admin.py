from django.contrib import admin
from django.utils.html import format_html
from .models import (
    Department, DocumentType, DocumentStatus,
    IncomingDocument, OutgoingDocument, DocumentTransfer,
    DocumentAction, DocumentComment
)


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'head', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name', 'code', 'description']
    ordering = ['name']


@admin.register(DocumentType)
class DocumentTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'code']
    search_fields = ['name', 'code']
    ordering = ['name']


@admin.register(DocumentStatus)
class DocumentStatusAdmin(admin.ModelAdmin):
    list_display = ['name', 'color_preview']
    search_fields = ['name']

    def color_preview(self, obj):
        return format_html(
            '<span style="background-color: {}; padding: 5px; border-radius: 3px; color: white;">{}</span>',
            obj.color, obj.name
        )
    color_preview.short_description = 'معاينة اللون'


class DocumentTransferInline(admin.TabularInline):
    model = DocumentTransfer
    extra = 0
    readonly_fields = ['transfer_date', 'received_date']


class DocumentActionInline(admin.TabularInline):
    model = DocumentAction
    extra = 0
    readonly_fields = ['action_type', 'description', 'performed_by', 'performed_at']


class DocumentCommentInline(admin.TabularInline):
    model = DocumentComment
    extra = 0
    readonly_fields = ['comment', 'commented_by', 'commented_at']


@admin.register(IncomingDocument)
class IncomingDocumentAdmin(admin.ModelAdmin):
    list_display = ['incoming_number', 'document_number', 'sender_entity', 'subject_short',
                   'status', 'priority', 'assigned_to_department', 'incoming_date']
    list_filter = ['status', 'priority', 'document_type', 'assigned_to_department', 'incoming_date']
    search_fields = ['incoming_number', 'document_number', 'sender_entity', 'subject']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-incoming_date']

    fieldsets = (
        ('معلومات الكتاب', {
            'fields': ('document_number', 'document_date', 'sender_entity', 'subject')
        }),
        ('معلومات الورود', {
            'fields': ('incoming_number', 'incoming_date', 'received_by')
        }),
        ('التصنيف والحالة', {
            'fields': ('document_type', 'status', 'priority')
        }),
        ('التحويل', {
            'fields': ('assigned_to_department', 'assigned_to_user')
        }),
        ('الملفات والمرفقات', {
            'fields': ('document_file',)
        }),
        ('المتابعة', {
            'fields': ('notes', 'deadline')
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [DocumentTransferInline, DocumentActionInline, DocumentCommentInline]

    def subject_short(self, obj):
        return obj.subject[:50] + '...' if len(obj.subject) > 50 else obj.subject
    subject_short.short_description = 'الموضوع'


@admin.register(OutgoingDocument)
class OutgoingDocumentAdmin(admin.ModelAdmin):
    list_display = ['document_number', 'recipient_entity', 'subject_short',
                   'status', 'priority', 'department', 'document_date']
    list_filter = ['status', 'priority', 'document_type', 'department', 'document_date']
    search_fields = ['document_number', 'recipient_entity', 'subject']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-document_date']

    fieldsets = (
        ('معلومات الكتاب', {
            'fields': ('document_number', 'document_date', 'recipient_entity', 'recipient_address')
        }),
        ('المحتوى', {
            'fields': ('subject', 'content')
        }),
        ('التصنيف', {
            'fields': ('document_type', 'department', 'status', 'priority')
        }),
        ('المسؤولون', {
            'fields': ('prepared_by', 'approved_by', 'signed_by')
        }),
        ('الإرسال', {
            'fields': ('sent_date', 'delivery_method')
        }),
        ('الملفات والملاحظات', {
            'fields': ('document_file', 'notes')
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [DocumentActionInline, DocumentCommentInline]

    def subject_short(self, obj):
        return obj.subject[:50] + '...' if len(obj.subject) > 50 else obj.subject
    subject_short.short_description = 'الموضوع'


@admin.register(DocumentTransfer)
class DocumentTransferAdmin(admin.ModelAdmin):
    list_display = ['incoming_document', 'from_department', 'to_department',
                   'from_user', 'transfer_date', 'is_received']
    list_filter = ['is_received', 'transfer_date', 'from_department', 'to_department']
    search_fields = ['incoming_document__incoming_number', 'notes']
    readonly_fields = ['transfer_date']
    ordering = ['-transfer_date']


@admin.register(DocumentAction)
class DocumentActionAdmin(admin.ModelAdmin):
    list_display = ['get_document', 'action_type', 'performed_by', 'performed_at']
    list_filter = ['action_type', 'performed_at']
    search_fields = ['description', 'performed_by__username']
    readonly_fields = ['performed_at']
    ordering = ['-performed_at']

    def get_document(self, obj):
        return obj.incoming_document or obj.outgoing_document
    get_document.short_description = 'الكتاب'


@admin.register(DocumentComment)
class DocumentCommentAdmin(admin.ModelAdmin):
    list_display = ['get_document', 'commented_by', 'comment_short', 'commented_at', 'is_internal']
    list_filter = ['is_internal', 'commented_at']
    search_fields = ['comment', 'commented_by__username']
    readonly_fields = ['commented_at']
    ordering = ['-commented_at']

    def get_document(self, obj):
        return obj.incoming_document or obj.outgoing_document
    get_document.short_description = 'الكتاب'

    def comment_short(self, obj):
        return obj.comment[:50] + '...' if len(obj.comment) > 50 else obj.comment
    comment_short.short_description = 'التعليق'

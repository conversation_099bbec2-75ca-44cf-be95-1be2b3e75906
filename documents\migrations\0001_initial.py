# Generated by Django 5.2.4 on 2025-07-07 17:35

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import documents.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='حالة الكتاب')),
                ('color', models.CharField(default='#007bff', max_length=7, verbose_name='لون الحالة')),
            ],
            options={
                'verbose_name': 'حالة الكتاب',
                'verbose_name_plural': 'حالات الكتب',
            },
        ),
        migrations.CreateModel(
            name='DocumentType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='نوع الكتاب')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='رمز النوع')),
            ],
            options={
                'verbose_name': 'نوع الكتاب',
                'verbose_name_plural': 'أنواع الكتب',
            },
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القسم')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='رمز القسم')),
                ('description', models.TextField(blank=True, verbose_name='وصف القسم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('head', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='headed_departments', to=settings.AUTH_USER_MODEL, verbose_name='رئيس القسم')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='IncomingDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_number', models.CharField(max_length=50, verbose_name='رقم الكتاب')),
                ('document_date', models.DateField(verbose_name='تاريخ الكتاب')),
                ('sender_entity', models.CharField(max_length=200, verbose_name='الجهة المرسلة')),
                ('subject', models.TextField(verbose_name='موضوع الكتاب')),
                ('incoming_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الورود')),
                ('incoming_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ الورود')),
                ('priority', models.CharField(choices=[('low', 'عادي'), ('medium', 'مهم'), ('high', 'عاجل'), ('urgent', 'عاجل جداً')], default='medium', max_length=20, verbose_name='الأولوية')),
                ('document_file', models.FileField(upload_to=documents.models.document_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'])], verbose_name='ملف الكتاب')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('deadline', models.DateField(blank=True, null=True, verbose_name='الموعد النهائي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإدخال')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('assigned_to_department', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='documents.department', verbose_name='القسم المحول إليه')),
                ('assigned_to_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_documents', to=settings.AUTH_USER_MODEL, verbose_name='المحول إليه')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_incoming_documents', to=settings.AUTH_USER_MODEL, verbose_name='أدخل بواسطة')),
                ('document_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='documents.documenttype', verbose_name='نوع الكتاب')),
                ('received_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='received_documents', to=settings.AUTH_USER_MODEL, verbose_name='المستلم')),
                ('status', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='documents.documentstatus', verbose_name='الحالة')),
            ],
            options={
                'verbose_name': 'كتاب وارد',
                'verbose_name_plural': 'الكتب الواردة',
                'ordering': ['-incoming_date'],
            },
        ),
        migrations.CreateModel(
            name='DocumentTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ التحويل')),
                ('received_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاستلام')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات التحويل')),
                ('instructions', models.TextField(blank=True, verbose_name='تعليمات خاصة')),
                ('is_received', models.BooleanField(default=False, verbose_name='تم الاستلام')),
                ('from_department', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_from', to='documents.department', verbose_name='من القسم')),
                ('from_user', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_from_user', to=settings.AUTH_USER_MODEL, verbose_name='المحول')),
                ('received_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='received_transfers', to=settings.AUTH_USER_MODEL, verbose_name='استلم بواسطة')),
                ('to_department', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_to', to='documents.department', verbose_name='إلى القسم')),
                ('to_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transfers_to_user', to=settings.AUTH_USER_MODEL, verbose_name='المحول إليه')),
                ('incoming_document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers', to='documents.incomingdocument', verbose_name='الكتاب الوارد')),
            ],
            options={
                'verbose_name': 'تحويل داخلي',
                'verbose_name_plural': 'التحويلات الداخلية',
                'ordering': ['-transfer_date'],
            },
        ),
        migrations.CreateModel(
            name='OutgoingDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الكتاب')),
                ('document_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ الكتاب')),
                ('recipient_entity', models.CharField(max_length=200, verbose_name='الجهة المرسل إليها')),
                ('recipient_address', models.TextField(blank=True, verbose_name='عنوان الجهة')),
                ('subject', models.TextField(verbose_name='موضوع الكتاب')),
                ('content', models.TextField(verbose_name='نص الكتاب')),
                ('document_file', models.FileField(blank=True, upload_to=documents.models.document_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'doc', 'docx'])], verbose_name='ملف الكتاب')),
                ('priority', models.CharField(choices=[('low', 'عادي'), ('medium', 'مهم'), ('high', 'عاجل'), ('urgent', 'عاجل جداً')], default='medium', max_length=20, verbose_name='الأولوية')),
                ('sent_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإرسال')),
                ('delivery_method', models.CharField(choices=[('hand', 'تسليم يد'), ('mail', 'بريد عادي'), ('email', 'بريد إلكتروني'), ('fax', 'فاكس'), ('courier', 'مندوب')], default='hand', max_length=50, verbose_name='طريقة التسليم')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_documents', to=settings.AUTH_USER_MODEL, verbose_name='وافق عليه')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='documents.department', verbose_name='القسم المرسل')),
                ('document_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='documents.documenttype', verbose_name='نوع الكتاب')),
                ('prepared_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='prepared_documents', to=settings.AUTH_USER_MODEL, verbose_name='أعد بواسطة')),
                ('signed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='signed_documents', to=settings.AUTH_USER_MODEL, verbose_name='وقع بواسطة')),
                ('status', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='documents.documentstatus', verbose_name='الحالة')),
            ],
            options={
                'verbose_name': 'كتاب صادر',
                'verbose_name_plural': 'الكتب الصادرة',
                'ordering': ['-document_date'],
            },
        ),
        migrations.CreateModel(
            name='DocumentComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('comment', models.TextField(verbose_name='التعليق')),
                ('commented_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ التعليق')),
                ('is_internal', models.BooleanField(default=True, verbose_name='تعليق داخلي')),
                ('commented_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='علق بواسطة')),
                ('incoming_document', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='documents.incomingdocument', verbose_name='الكتاب الوارد')),
                ('outgoing_document', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='documents.outgoingdocument', verbose_name='الكتاب الصادر')),
            ],
            options={
                'verbose_name': 'تعليق',
                'verbose_name_plural': 'التعليقات',
                'ordering': ['-commented_at'],
            },
        ),
        migrations.CreateModel(
            name='DocumentAction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('create', 'إنشاء'), ('update', 'تحديث'), ('transfer', 'تحويل'), ('receive', 'استلام'), ('approve', 'موافقة'), ('reject', 'رفض'), ('complete', 'إنجاز'), ('archive', 'أرشفة'), ('delete', 'حذف')], max_length=20, verbose_name='نوع الإجراء')),
                ('description', models.TextField(verbose_name='وصف الإجراء')),
                ('performed_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ التنفيذ')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='معلومات المتصفح')),
                ('performed_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='نفذ بواسطة')),
                ('incoming_document', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='actions', to='documents.incomingdocument', verbose_name='الكتاب الوارد')),
                ('outgoing_document', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='actions', to='documents.outgoingdocument', verbose_name='الكتاب الصادر')),
            ],
            options={
                'verbose_name': 'إجراء',
                'verbose_name_plural': 'سجل الإجراءات',
                'ordering': ['-performed_at'],
            },
        ),
    ]

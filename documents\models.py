from django.db import models
from django.conf import settings
from django.utils import timezone
from django.core.validators import FileExtensionValidator


class Department(models.Model):
    """نموذج الأقسام"""
    name = models.CharField(max_length=100, verbose_name="اسم القسم")
    code = models.CharField(max_length=10, unique=True, verbose_name="رمز القسم")
    description = models.TextField(blank=True, verbose_name="وصف القسم")
    head = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True,
                           related_name='headed_departments', verbose_name="رئيس القسم")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "قسم"
        verbose_name_plural = "الأقسام"
        ordering = ['name']

    def __str__(self):
        return self.name


class DocumentType(models.Model):
    """أنواع الكتب"""
    name = models.CharField(max_length=50, verbose_name="نوع الكتاب")
    code = models.CharField(max_length=10, unique=True, verbose_name="رمز النوع")

    class Meta:
        verbose_name = "نوع الكتاب"
        verbose_name_plural = "أنواع الكتب"

    def __str__(self):
        return self.name


class DocumentStatus(models.Model):
    """حالات الكتب"""
    name = models.CharField(max_length=50, verbose_name="حالة الكتاب")
    color = models.CharField(max_length=7, default="#007bff", verbose_name="لون الحالة")

    class Meta:
        verbose_name = "حالة الكتاب"
        verbose_name_plural = "حالات الكتب"

    def __str__(self):
        return self.name


def document_upload_path(instance, filename):
    """تحديد مسار رفع الملفات"""
    if hasattr(instance, 'incoming_number'):
        # للكتب الواردة
        return f'documents/incoming/{timezone.now().year}/{instance.incoming_number}_{filename}'
    else:
        # للكتب الصادرة
        return f'documents/outgoing/{timezone.now().year}/{instance.document_number}_{filename}'


class IncomingDocument(models.Model):
    """نموذج الكتب الواردة"""
    # معلومات الكتاب الأساسية
    document_number = models.CharField(max_length=50, verbose_name="رقم الكتاب")
    document_date = models.DateField(verbose_name="تاريخ الكتاب")
    sender_entity = models.CharField(max_length=200, verbose_name="الجهة المرسلة")
    subject = models.TextField(verbose_name="موضوع الكتاب")

    # معلومات الورود
    incoming_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الورود")
    incoming_date = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الورود")
    received_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT,
                                  related_name='received_documents', verbose_name="المستلم")

    # التصنيف والحالة
    document_type = models.ForeignKey(DocumentType, on_delete=models.PROTECT, verbose_name="نوع الكتاب")
    status = models.ForeignKey(DocumentStatus, on_delete=models.PROTECT, verbose_name="الحالة")
    priority = models.CharField(max_length=20, choices=[
        ('low', 'عادي'),
        ('medium', 'مهم'),
        ('high', 'عاجل'),
        ('urgent', 'عاجل جداً')
    ], default='medium', verbose_name="الأولوية")

    # الملفات والمرفقات
    document_file = models.FileField(
        upload_to=document_upload_path,
        validators=[FileExtensionValidator(allowed_extensions=['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'])],
        verbose_name="ملف الكتاب"
    )

    # التحويل والمتابعة
    assigned_to_department = models.ForeignKey(Department, on_delete=models.PROTECT,
                                             verbose_name="القسم المحول إليه")
    assigned_to_user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True,
                                       related_name='assigned_documents', verbose_name="المحول إليه")

    # ملاحظات ومتابعة
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    deadline = models.DateField(null=True, blank=True, verbose_name="الموعد النهائي")

    # معلومات النظام
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإدخال")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT,
                                 related_name='created_incoming_documents', verbose_name="أدخل بواسطة")

    class Meta:
        verbose_name = "كتاب وارد"
        verbose_name_plural = "الكتب الواردة"
        ordering = ['-incoming_date']

    def __str__(self):
        return f"{self.incoming_number} - {self.subject[:50]}"

    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('documents:incoming_detail', kwargs={'pk': self.pk})


class OutgoingDocument(models.Model):
    """نموذج الكتب الصادرة"""
    # رقم الكتاب بالصيغة العراقية (القسم/السنة/الرقم)
    document_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الكتاب")
    document_date = models.DateField(default=timezone.now, verbose_name="تاريخ الكتاب")

    # معلومات المرسل إليه
    recipient_entity = models.CharField(max_length=200, verbose_name="الجهة المرسل إليها")
    recipient_address = models.TextField(blank=True, verbose_name="عنوان الجهة")

    # محتوى الكتاب
    subject = models.TextField(verbose_name="موضوع الكتاب")
    content = models.TextField(verbose_name="نص الكتاب")

    # التصنيف
    document_type = models.ForeignKey(DocumentType, on_delete=models.PROTECT, verbose_name="نوع الكتاب")
    department = models.ForeignKey(Department, on_delete=models.PROTECT, verbose_name="القسم المرسل")

    # المسؤولون
    prepared_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT,
                                  related_name='prepared_documents', verbose_name="أعد بواسطة")
    approved_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='approved_documents', verbose_name="وافق عليه")
    signed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True,
                                related_name='signed_documents', verbose_name="وقع بواسطة")

    # الملفات
    document_file = models.FileField(
        upload_to=document_upload_path,
        validators=[FileExtensionValidator(allowed_extensions=['pdf', 'doc', 'docx'])],
        blank=True, verbose_name="ملف الكتاب"
    )

    # الحالة والأولوية
    status = models.ForeignKey(DocumentStatus, on_delete=models.PROTECT, verbose_name="الحالة")
    priority = models.CharField(max_length=20, choices=[
        ('low', 'عادي'),
        ('medium', 'مهم'),
        ('high', 'عاجل'),
        ('urgent', 'عاجل جداً')
    ], default='medium', verbose_name="الأولوية")

    # الإرسال
    sent_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الإرسال")
    delivery_method = models.CharField(max_length=50, choices=[
        ('hand', 'تسليم يد'),
        ('mail', 'بريد عادي'),
        ('email', 'بريد إلكتروني'),
        ('fax', 'فاكس'),
        ('courier', 'مندوب')
    ], default='hand', verbose_name="طريقة التسليم")

    # ملاحظات
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    # معلومات النظام
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "كتاب صادر"
        verbose_name_plural = "الكتب الصادرة"
        ordering = ['-document_date']

    def __str__(self):
        return f"{self.document_number} - {self.subject[:50]}"

    def save(self, *args, **kwargs):
        if not self.document_number:
            # توليد رقم الكتاب بالصيغة العراقية
            year = timezone.now().year
            department_code = self.department.code

            # البحث عن آخر رقم للقسم في هذه السنة
            last_doc = OutgoingDocument.objects.filter(
                document_number__startswith=f"{department_code}/{year}/"
            ).order_by('-document_number').first()

            if last_doc:
                # استخراج الرقم التسلسلي من آخر كتاب
                try:
                    last_number = int(last_doc.document_number.split('/')[-1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1

            self.document_number = f"{department_code}/{year}/{new_number}"

        super().save(*args, **kwargs)

    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('documents:outgoing_detail', kwargs={'pk': self.pk})


class DocumentTransfer(models.Model):
    """نموذج التحويلات الداخلية"""
    # الكتاب المحول
    incoming_document = models.ForeignKey(IncomingDocument, on_delete=models.CASCADE,
                                        related_name='transfers', verbose_name="الكتاب الوارد")

    # معلومات التحويل
    from_department = models.ForeignKey(Department, on_delete=models.PROTECT,
                                      related_name='transfers_from', verbose_name="من القسم")
    to_department = models.ForeignKey(Department, on_delete=models.PROTECT,
                                    related_name='transfers_to', verbose_name="إلى القسم")
    from_user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT,
                                related_name='transfers_from_user', verbose_name="المحول")
    to_user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True,
                              related_name='transfers_to_user', verbose_name="المحول إليه")

    # تفاصيل التحويل
    transfer_date = models.DateTimeField(default=timezone.now, verbose_name="تاريخ التحويل")
    received_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاستلام")
    notes = models.TextField(blank=True, verbose_name="ملاحظات التحويل")
    instructions = models.TextField(blank=True, verbose_name="تعليمات خاصة")

    # الحالة
    is_received = models.BooleanField(default=False, verbose_name="تم الاستلام")
    received_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='received_transfers', verbose_name="استلم بواسطة")

    class Meta:
        verbose_name = "تحويل داخلي"
        verbose_name_plural = "التحويلات الداخلية"
        ordering = ['-transfer_date']

    def __str__(self):
        return f"تحويل {self.incoming_document.incoming_number} من {self.from_department} إلى {self.to_department}"


class DocumentAction(models.Model):
    """نموذج سجل الأحداث والإجراءات"""
    ACTION_TYPES = [
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('transfer', 'تحويل'),
        ('receive', 'استلام'),
        ('approve', 'موافقة'),
        ('reject', 'رفض'),
        ('complete', 'إنجاز'),
        ('archive', 'أرشفة'),
        ('delete', 'حذف'),
    ]

    # الكتاب المرتبط
    incoming_document = models.ForeignKey(IncomingDocument, on_delete=models.CASCADE,
                                        null=True, blank=True, related_name='actions',
                                        verbose_name="الكتاب الوارد")
    outgoing_document = models.ForeignKey(OutgoingDocument, on_delete=models.CASCADE,
                                        null=True, blank=True, related_name='actions',
                                        verbose_name="الكتاب الصادر")

    # تفاصيل الإجراء
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES, verbose_name="نوع الإجراء")
    description = models.TextField(verbose_name="وصف الإجراء")
    performed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT, verbose_name="نفذ بواسطة")
    performed_at = models.DateTimeField(default=timezone.now, verbose_name="تاريخ التنفيذ")

    # معلومات إضافية
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name="عنوان IP")
    user_agent = models.TextField(blank=True, verbose_name="معلومات المتصفح")

    class Meta:
        verbose_name = "إجراء"
        verbose_name_plural = "سجل الإجراءات"
        ordering = ['-performed_at']

    def __str__(self):
        doc_ref = self.incoming_document or self.outgoing_document
        return f"{self.get_action_type_display()} - {doc_ref} - {self.performed_by}"


class DocumentComment(models.Model):
    """نموذج التعليقات والملاحظات"""
    # الكتاب المرتبط
    incoming_document = models.ForeignKey(IncomingDocument, on_delete=models.CASCADE,
                                        null=True, blank=True, related_name='comments',
                                        verbose_name="الكتاب الوارد")
    outgoing_document = models.ForeignKey(OutgoingDocument, on_delete=models.CASCADE,
                                        null=True, blank=True, related_name='comments',
                                        verbose_name="الكتاب الصادر")

    # التعليق
    comment = models.TextField(verbose_name="التعليق")
    commented_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT, verbose_name="علق بواسطة")
    commented_at = models.DateTimeField(default=timezone.now, verbose_name="تاريخ التعليق")

    # الحالة
    is_internal = models.BooleanField(default=True, verbose_name="تعليق داخلي")

    class Meta:
        verbose_name = "تعليق"
        verbose_name_plural = "التعليقات"
        ordering = ['-commented_at']

    def __str__(self):
        doc_ref = self.incoming_document or self.outgoing_document
        return f"تعليق على {doc_ref} - {self.commented_by}"

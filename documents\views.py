from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from .models import (
    IncomingDocument, OutgoingDocument, Department,
    DocumentType, DocumentStatus, DocumentTransfer
)


@login_required
def dashboard_view(request):
    """الصفحة الرئيسية للنظام"""
    # إحصائيات سريعة
    total_incoming = IncomingDocument.objects.count()
    total_outgoing = OutgoingDocument.objects.count()
    pending_incoming = IncomingDocument.objects.filter(
        status__name__in=['جديد', 'قيد المراجعة', 'قيد الإنجاز']
    ).count()

    # الكتب الحديثة
    recent_incoming = IncomingDocument.objects.select_related(
        'status', 'document_type', 'assigned_to_department'
    ).order_by('-incoming_date')[:5]

    recent_outgoing = OutgoingDocument.objects.select_related(
        'status', 'document_type', 'department'
    ).order_by('-document_date')[:5]

    # التحويلات المعلقة
    pending_transfers = DocumentTransfer.objects.filter(
        is_received=False
    ).select_related(
        'incoming_document', 'from_department', 'to_department'
    )[:5]

    context = {
        'total_incoming': total_incoming,
        'total_outgoing': total_outgoing,
        'pending_incoming': pending_incoming,
        'recent_incoming': recent_incoming,
        'recent_outgoing': recent_outgoing,
        'pending_transfers': pending_transfers,
    }

    return render(request, 'dashboard.html', context)


@login_required
def incoming_documents_list(request):
    """قائمة الكتب الواردة"""
    documents = IncomingDocument.objects.select_related(
        'status', 'document_type', 'assigned_to_department', 'received_by'
    ).order_by('-incoming_date')

    # البحث والتصفية
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    department_filter = request.GET.get('department', '')
    type_filter = request.GET.get('type', '')

    if search_query:
        documents = documents.filter(
            Q(incoming_number__icontains=search_query) |
            Q(document_number__icontains=search_query) |
            Q(sender_entity__icontains=search_query) |
            Q(subject__icontains=search_query)
        )

    if status_filter:
        documents = documents.filter(status_id=status_filter)

    if department_filter:
        documents = documents.filter(assigned_to_department_id=department_filter)

    if type_filter:
        documents = documents.filter(document_type_id=type_filter)

    # التصفح
    paginator = Paginator(documents, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # البيانات للفلاتر
    statuses = DocumentStatus.objects.all()
    departments = Department.objects.all()
    document_types = DocumentType.objects.all()

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'department_filter': department_filter,
        'type_filter': type_filter,
        'statuses': statuses,
        'departments': departments,
        'document_types': document_types,
    }

    return render(request, 'documents/incoming_list.html', context)


@login_required
def incoming_document_detail(request, pk):
    """تفاصيل الكتاب الوارد"""
    document = get_object_or_404(
        IncomingDocument.objects.select_related(
            'status', 'document_type', 'assigned_to_department',
            'received_by', 'created_by'
        ).prefetch_related('transfers', 'actions', 'comments'),
        pk=pk
    )

    context = {
        'document': document,
        'transfers': document.transfers.select_related(
            'from_department', 'to_department', 'from_user', 'to_user'
        ).order_by('-transfer_date'),
        'actions': document.actions.select_related('performed_by').order_by('-performed_at'),
        'comments': document.comments.select_related('commented_by').order_by('-commented_at'),
    }

    return render(request, 'documents/incoming_detail.html', context)


@login_required
def outgoing_documents_list(request):
    """قائمة الكتب الصادرة"""
    documents = OutgoingDocument.objects.select_related(
        'status', 'document_type', 'department', 'prepared_by'
    ).order_by('-document_date')

    # البحث والتصفية
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    department_filter = request.GET.get('department', '')
    type_filter = request.GET.get('type', '')

    if search_query:
        documents = documents.filter(
            Q(document_number__icontains=search_query) |
            Q(recipient_entity__icontains=search_query) |
            Q(subject__icontains=search_query)
        )

    if status_filter:
        documents = documents.filter(status_id=status_filter)

    if department_filter:
        documents = documents.filter(department_id=department_filter)

    if type_filter:
        documents = documents.filter(document_type_id=type_filter)

    # التصفح
    paginator = Paginator(documents, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # البيانات للفلاتر
    statuses = DocumentStatus.objects.all()
    departments = Department.objects.all()
    document_types = DocumentType.objects.all()

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'department_filter': department_filter,
        'type_filter': type_filter,
        'statuses': statuses,
        'departments': departments,
        'document_types': document_types,
    }

    return render(request, 'documents/outgoing_list.html', context)


@login_required
def outgoing_document_detail(request, pk):
    """تفاصيل الكتاب الصادر"""
    document = get_object_or_404(
        OutgoingDocument.objects.select_related(
            'status', 'document_type', 'department',
            'prepared_by', 'approved_by', 'signed_by'
        ).prefetch_related('actions', 'comments'),
        pk=pk
    )

    context = {
        'document': document,
        'actions': document.actions.select_related('performed_by').order_by('-performed_at'),
        'comments': document.comments.select_related('commented_by').order_by('-commented_at'),
    }

    return render(request, 'documents/outgoing_detail.html', context)

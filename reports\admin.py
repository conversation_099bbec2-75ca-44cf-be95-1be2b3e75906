from django.contrib import admin
from .models import ReportTemplate, GeneratedReport, ReportSchedule


@admin.register(ReportTemplate)
class ReportTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'report_type', 'created_by', 'created_at', 'is_public']
    list_filter = ['report_type', 'is_public', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at']
    ordering = ['name']

    fieldsets = (
        ('معلومات القالب', {
            'fields': ('name', 'description', 'report_type', 'is_public')
        }),
        ('إعدادات القالب', {
            'fields': ('fields_to_include', 'filters', 'sorting'),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(GeneratedReport)
class GeneratedReportAdmin(admin.ModelAdmin):
    list_display = ['title', 'template', 'date_from', 'date_to', 'file_format',
                   'total_records', 'generated_by', 'generated_at']
    list_filter = ['file_format', 'generated_at', 'template']
    search_fields = ['title', 'generated_by__username']
    readonly_fields = ['generated_at', 'generation_time', 'total_records']
    ordering = ['-generated_at']

    fieldsets = (
        ('معلومات التقرير', {
            'fields': ('title', 'template', 'file_format')
        }),
        ('معايير التقرير', {
            'fields': ('date_from', 'date_to', 'departments', 'document_types')
        }),
        ('الملف والإحصائيات', {
            'fields': ('report_file', 'total_records', 'generation_time')
        }),
        ('معلومات النظام', {
            'fields': ('generated_by', 'generated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ReportSchedule)
class ReportScheduleAdmin(admin.ModelAdmin):
    list_display = ['name', 'template', 'frequency', 'is_active', 'last_run', 'next_run']
    list_filter = ['frequency', 'is_active', 'created_at']
    search_fields = ['name', 'email_subject']
    readonly_fields = ['created_at', 'last_run']
    ordering = ['name']

    fieldsets = (
        ('معلومات الجدولة', {
            'fields': ('name', 'template', 'frequency', 'is_active')
        }),
        ('إعدادات البريد الإلكتروني', {
            'fields': ('recipients', 'email_subject', 'email_body')
        }),
        ('معلومات التشغيل', {
            'fields': ('last_run', 'next_run')
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at'),
            'classes': ('collapse',)
        }),
    )

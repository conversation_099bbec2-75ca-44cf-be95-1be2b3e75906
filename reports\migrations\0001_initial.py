# Generated by Django 5.2.4 on 2025-07-07 17:35

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القالب')),
                ('description', models.TextField(blank=True, verbose_name='وصف القالب')),
                ('report_type', models.CharField(choices=[('incoming', 'الكتب الواردة'), ('outgoing', 'الكتب الصادرة'), ('transfers', 'التحويلات'), ('statistics', 'إحصائيات'), ('activities', 'الأنشطة')], max_length=50, verbose_name='نوع التقرير')),
                ('fields_to_include', models.JSONField(default=list, verbose_name='الحقول المضمنة')),
                ('filters', models.JSONField(default=dict, verbose_name='المرشحات')),
                ('sorting', models.JSONField(default=dict, verbose_name='الترتيب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('is_public', models.BooleanField(default=False, verbose_name='قالب عام')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
            ],
            options={
                'verbose_name': 'قالب تقرير',
                'verbose_name_plural': 'قوالب التقارير',
            },
        ),
        migrations.CreateModel(
            name='ReportSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الجدولة')),
                ('frequency', models.CharField(choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري'), ('quarterly', 'ربع سنوي'), ('yearly', 'سنوي')], max_length=20, verbose_name='التكرار')),
                ('email_subject', models.CharField(max_length=200, verbose_name='موضوع البريد')),
                ('email_body', models.TextField(verbose_name='نص البريد')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('last_run', models.DateTimeField(blank=True, null=True, verbose_name='آخر تشغيل')),
                ('next_run', models.DateTimeField(verbose_name='التشغيل التالي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_schedules', to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('recipients', models.ManyToManyField(to=settings.AUTH_USER_MODEL, verbose_name='المستلمون')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='reports.reporttemplate', verbose_name='قالب التقرير')),
            ],
            options={
                'verbose_name': 'جدولة تقرير',
                'verbose_name_plural': 'جدولة التقارير',
            },
        ),
        migrations.CreateModel(
            name='GeneratedReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التقرير')),
                ('date_from', models.DateField(verbose_name='من تاريخ')),
                ('date_to', models.DateField(verbose_name='إلى تاريخ')),
                ('departments', models.JSONField(default=list, verbose_name='الأقسام')),
                ('document_types', models.JSONField(default=list, verbose_name='أنواع الكتب')),
                ('report_file', models.FileField(blank=True, upload_to='reports/', verbose_name='ملف التقرير')),
                ('file_format', models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV')], default='pdf', max_length=10, verbose_name='صيغة الملف')),
                ('total_records', models.IntegerField(default=0, verbose_name='إجمالي السجلات')),
                ('generation_time', models.DurationField(blank=True, null=True, verbose_name='وقت الإنشاء')),
                ('generated_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ الإنشاء')),
                ('generated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='reports.reporttemplate', verbose_name='القالب المستخدم')),
            ],
            options={
                'verbose_name': 'تقرير منشأ',
                'verbose_name_plural': 'التقارير المنشأة',
                'ordering': ['-generated_at'],
            },
        ),
    ]

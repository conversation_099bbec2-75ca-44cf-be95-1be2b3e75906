from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()


class ReportTemplate(models.Model):
    """قوالب التقارير"""
    name = models.CharField(max_length=100, verbose_name="اسم القالب")
    description = models.TextField(blank=True, verbose_name="وصف القالب")
    report_type = models.CharField(max_length=50, choices=[
        ('incoming', 'الكتب الواردة'),
        ('outgoing', 'الكتب الصادرة'),
        ('transfers', 'التحويلات'),
        ('statistics', 'إحصائيات'),
        ('activities', 'الأنشطة'),
    ], verbose_name="نوع التقرير")

    # إعدادات القالب
    fields_to_include = models.JSONField(default=list, verbose_name="الحقول المضمنة")
    filters = models.JSONField(default=dict, verbose_name="المرشحات")
    sorting = models.JSONField(default=dict, verbose_name="الترتيب")

    # معلومات النظام
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشأ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    is_public = models.BooleanField(default=False, verbose_name="قالب عام")

    class Meta:
        verbose_name = "قالب تقرير"
        verbose_name_plural = "قوالب التقارير"

    def __str__(self):
        return self.name


class GeneratedReport(models.Model):
    """التقارير المُنشأة"""
    title = models.CharField(max_length=200, verbose_name="عنوان التقرير")
    template = models.ForeignKey(ReportTemplate, on_delete=models.SET_NULL,
                               null=True, blank=True, verbose_name="القالب المستخدم")

    # معايير التقرير
    date_from = models.DateField(verbose_name="من تاريخ")
    date_to = models.DateField(verbose_name="إلى تاريخ")
    departments = models.JSONField(default=list, verbose_name="الأقسام")
    document_types = models.JSONField(default=list, verbose_name="أنواع الكتب")

    # ملف التقرير
    report_file = models.FileField(upload_to='reports/', blank=True, verbose_name="ملف التقرير")
    file_format = models.CharField(max_length=10, choices=[
        ('pdf', 'PDF'),
        ('excel', 'Excel'),
        ('csv', 'CSV'),
    ], default='pdf', verbose_name="صيغة الملف")

    # إحصائيات التقرير
    total_records = models.IntegerField(default=0, verbose_name="إجمالي السجلات")
    generation_time = models.DurationField(null=True, blank=True, verbose_name="وقت الإنشاء")

    # معلومات النظام
    generated_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشأ بواسطة")
    generated_at = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "تقرير منشأ"
        verbose_name_plural = "التقارير المنشأة"
        ordering = ['-generated_at']

    def __str__(self):
        return f"{self.title} - {self.generated_at.strftime('%Y-%m-%d')}"


class ReportSchedule(models.Model):
    """جدولة التقارير"""
    name = models.CharField(max_length=100, verbose_name="اسم الجدولة")
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE, verbose_name="قالب التقرير")

    # إعدادات الجدولة
    frequency = models.CharField(max_length=20, choices=[
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري'),
        ('quarterly', 'ربع سنوي'),
        ('yearly', 'سنوي'),
    ], verbose_name="التكرار")

    # المستلمون
    recipients = models.ManyToManyField(User, verbose_name="المستلمون")
    email_subject = models.CharField(max_length=200, verbose_name="موضوع البريد")
    email_body = models.TextField(verbose_name="نص البريد")

    # الحالة
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    last_run = models.DateTimeField(null=True, blank=True, verbose_name="آخر تشغيل")
    next_run = models.DateTimeField(verbose_name="التشغيل التالي")

    # معلومات النظام
    created_by = models.ForeignKey(User, on_delete=models.CASCADE,
                                 related_name='created_schedules', verbose_name="أنشأ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "جدولة تقرير"
        verbose_name_plural = "جدولة التقارير"

    def __str__(self):
        return f"{self.name} - {self.get_frequency_display()}"

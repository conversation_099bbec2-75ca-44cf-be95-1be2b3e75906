/* تصميم متقدم وجذاب للنظام */

:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --warning-gradient: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --shadow-soft: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --shadow-hover: 0 15px 35px rgba(31, 38, 135, 0.2);
    --border-radius: 16px;
    --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* خلفية متحركة */
body {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    position: relative;
    overflow-x: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تأثير الجسيمات المتحركة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.2) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 50px 50px, 30px 30px;
    animation: float 20s linear infinite;
    z-index: -1;
    pointer-events: none;
}

@keyframes float {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100px) rotate(360deg); }
}

/* شريط التنقل الزجاجي */
.navbar {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--shadow-soft);
    transition: var(--transition);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.4rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
}

/* الشريط الجانبي الزجاجي */
.sidebar {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-right: 1px solid var(--glass-border);
    box-shadow: var(--shadow-soft);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    margin: 0.5rem 1rem;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid transparent;
}

.sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    transition: var(--transition);
    z-index: -1;
}

.sidebar .nav-link:hover::before,
.sidebar .nav-link.active::before {
    left: 0;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    transform: translateX(8px) scale(1.02);
    box-shadow: var(--shadow-hover);
    border: 1px solid var(--glass-border);
}

.sidebar .nav-link i {
    margin-left: 0.75rem;
    width: 20px;
    transition: var(--transition);
}

.sidebar .nav-link:hover i {
    transform: scale(1.2) rotate(5deg);
}

/* البطاقات الزجاجية */
.card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-soft);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: var(--primary-gradient);
    transition: var(--transition);
}

.card:hover::before {
    left: 0;
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-hover);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.card-header {
    background: var(--primary-gradient);
    border: none;
    color: white;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.card-header::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(transparent, rgba(255,255,255,0.2), transparent);
    animation: rotate 4s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* الأزرار المتحركة */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: none;
    backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transition: var(--transition);
    transform: translate(-50%, -50%);
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn-primary {
    background: var(--primary-gradient);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.btn-success {
    background: var(--success-gradient);
    box-shadow: 0 4px 15px rgba(17, 153, 142, 0.4);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(17, 153, 142, 0.6);
}

.btn-warning {
    background: var(--warning-gradient);
    box-shadow: 0 4px 15px rgba(246, 211, 101, 0.4);
    color: #333;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(246, 211, 101, 0.6);
    color: #333;
}

.btn-danger {
    background: var(--danger-gradient);
    box-shadow: 0 4px 15px rgba(255, 154, 158, 0.4);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 154, 158, 0.6);
}

/* الجداول الزجاجية */
.table {
    background: transparent;
    backdrop-filter: blur(10px);
}

.table thead th {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    position: relative;
}

.table tbody tr {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: var(--transition);
}

.table tbody tr:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.01);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.table tbody td {
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

/* الشارات المتحركة */
.badge {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.badge::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: shine 2s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* النماذج المحسنة */
.form-control, .form-select {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    color: rgba(255, 255, 255, 0.9);
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    color: white;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-soft);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.stats-card:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: var(--shadow-hover);
}

.stats-number {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.5rem;
}

.stats-label {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* عنوان الصفحة */
.page-title {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-soft);
    color: rgba(255, 255, 255, 0.9);
    position: relative;
    overflow: hidden;
}

.page-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.page-title h1 {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* التنبيهات */
.alert {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    color: rgba(255, 255, 255, 0.9);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(20px);
    }
    
    .card:hover {
        transform: none;
    }
    
    .btn:hover {
        transform: none;
    }
}

/* تأثيرات التحميل */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

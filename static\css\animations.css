/* تأثيرات وحركات متقدمة */

/* تأثيرات الظهور */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.3);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-200deg);
    }
    to {
        opacity: 1;
        transform: rotate(0);
    }
}

/* تأثيرات التحويم */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

@keyframes swing {
    20% {
        transform: rotate(15deg);
    }
    40% {
        transform: rotate(-10deg);
    }
    60% {
        transform: rotate(5deg);
    }
    80% {
        transform: rotate(-5deg);
    }
    100% {
        transform: rotate(0deg);
    }
}

@keyframes wobble {
    0% {
        transform: translateX(0%);
    }
    15% {
        transform: translateX(-25%) rotate(-5deg);
    }
    30% {
        transform: translateX(20%) rotate(3deg);
    }
    45% {
        transform: translateX(-15%) rotate(-3deg);
    }
    60% {
        transform: translateX(10%) rotate(2deg);
    }
    75% {
        transform: translateX(-5%) rotate(-1deg);
    }
    100% {
        transform: translateX(0%);
    }
}

/* تأثيرات الخلفية */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* تأثيرات النص */
@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

@keyframes glow {
    0%, 100% {
        text-shadow: 0 0 5px currentColor;
    }
    50% {
        text-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
    }
}

/* تأثيرات الموجات */
@keyframes wave {
    0%, 100% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(-50%);
    }
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* تأثيرات التحميل */
@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes dots {
    0%, 20% {
        color: rgba(0,0,0,0);
        text-shadow: .25em 0 0 rgba(0,0,0,0), .5em 0 0 rgba(0,0,0,0);
    }
    40% {
        color: white;
        text-shadow: .25em 0 0 rgba(0,0,0,0), .5em 0 0 rgba(0,0,0,0);
    }
    60% {
        text-shadow: .25em 0 0 white, .5em 0 0 rgba(0,0,0,0);
    }
    80%, 100% {
        text-shadow: .25em 0 0 white, .5em 0 0 white;
    }
}

/* فئات الحركة */
.animate-fadeIn {
    animation: fadeIn 0.8s ease-out;
}

.animate-slideInLeft {
    animation: slideInLeft 0.8s ease-out;
}

.animate-slideInRight {
    animation: slideInRight 0.8s ease-out;
}

.animate-slideInUp {
    animation: slideInUp 0.8s ease-out;
}

.animate-slideInDown {
    animation: slideInDown 0.8s ease-out;
}

.animate-zoomIn {
    animation: zoomIn 0.8s ease-out;
}

.animate-rotateIn {
    animation: rotateIn 0.8s ease-out;
}

.animate-bounce {
    animation: bounce 1s;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-shake {
    animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
}

.animate-swing {
    animation: swing 1s ease-in-out;
}

.animate-wobble {
    animation: wobble 1s ease-in-out;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-rotate {
    animation: rotate 2s linear infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
}

/* تأثيرات التحويم */
.hover-lift:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
    transition: transform 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
    transition: box-shadow 0.3s ease;
}

/* تأثيرات التركيز */
.focus-scale:focus {
    transform: scale(1.02);
    transition: transform 0.2s ease;
}

.focus-glow:focus {
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
    transition: box-shadow 0.2s ease;
}

/* تأثيرات التأخير */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
.delay-600 { animation-delay: 0.6s; }
.delay-700 { animation-delay: 0.7s; }
.delay-800 { animation-delay: 0.8s; }
.delay-900 { animation-delay: 0.9s; }
.delay-1000 { animation-delay: 1s; }

/* تأثيرات السرعة */
.duration-fast { animation-duration: 0.3s; }
.duration-normal { animation-duration: 0.5s; }
.duration-slow { animation-duration: 1s; }
.duration-slower { animation-duration: 2s; }

/* تأثيرات التكرار */
.repeat-infinite { animation-iteration-count: infinite; }
.repeat-once { animation-iteration-count: 1; }
.repeat-twice { animation-iteration-count: 2; }

/* تأثيرات الاتجاه */
.direction-normal { animation-direction: normal; }
.direction-reverse { animation-direction: reverse; }
.direction-alternate { animation-direction: alternate; }
.direction-alternate-reverse { animation-direction: alternate-reverse; }

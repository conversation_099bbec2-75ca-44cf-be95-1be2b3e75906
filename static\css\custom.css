/* تحسينات إضافية للواجهة */

/* تحسين الجداول */
.table-sm td {
    padding: 0.5rem 0.75rem;
    vertical-align: middle;
}

.table-sm th {
    padding: 0.75rem;
    font-weight: 600;
    font-size: 13px;
}

/* تحسين الأزرار */
.btn-group-sm > .btn, .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 12px;
    border-radius: 4px;
}

/* تحسين البطاقات */
.card {
    transition: none;
}

.card:hover {
    transform: none;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* تحسين النماذج */
.form-control, .form-select {
    font-size: 14px;
}

/* تحسين الشارات */
.badge {
    font-size: 11px;
    font-weight: 500;
}

/* تحسين التنبيهات */
.alert {
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    font-size: 14px;
}

/* تحسين التصفح */
.pagination {
    margin-bottom: 0;
}

.page-link {
    padding: 0.5rem 0.75rem;
    font-size: 14px;
}

/* تحسين الشريط الجانبي */
.sidebar .nav-link {
    font-size: 14px;
    padding: 0.75rem 1.25rem;
}

/* تحسين العناوين */
h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

h2 {
    font-size: 1.25rem;
    font-weight: 600;
}

h3 {
    font-size: 1.1rem;
    font-weight: 600;
}

h4, h5, h6 {
    font-size: 1rem;
    font-weight: 600;
}

/* تحسين النصوص */
.text-muted {
    color: #6c757d !important;
}

.text-primary {
    color: #3498db !important;
}

/* تحسين المسافات */
.mb-3 {
    margin-bottom: 1rem !important;
}

.mt-3 {
    margin-top: 1rem !important;
}

/* تحسين الحدود */
.border {
    border: 1px solid #e0e0e0 !important;
}

/* تحسين الظلال */
.shadow-sm {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

/* تحسين الخلفيات */
.bg-light {
    background-color: #f8f9fa !important;
}

/* تحسين الروابط */
a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 6px;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 14px;
}

/* تحسين الأيقونات */
.fas, .far {
    width: 16px;
    text-align: center;
}

/* تحسين التخطيط المتجاوب */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }
    
    .page-title {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .page-title h1 {
        font-size: 1.25rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 0.25rem;
    }
    
    .table-responsive {
        font-size: 12px;
    }
    
    .stats-card {
        margin-bottom: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* تحسين الطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .page-title {
        border: none !important;
        box-shadow: none !important;
    }
}

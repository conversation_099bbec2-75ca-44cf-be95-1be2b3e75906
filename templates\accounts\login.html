{% extends 'base.html' %}

{% block title %}تسجيل الدخول - نظام إدارة الصادرة والواردة{% endblock %}

{% block extra_css %}
<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        position: relative;
        overflow: hidden;
    }

    .login-container::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: conic-gradient(from 0deg, #667eea, #764ba2, #667eea);
        animation: rotate 20s linear infinite;
        z-index: -1;
    }

    .login-card {
        background: rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.18);
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        width: 100%;
        max-width: 450px;
        position: relative;
        overflow: hidden;
        animation: slideUp 0.8s ease-out;
    }

    .login-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .login-header {
        text-align: center;
        margin-bottom: 2.5rem;
    }

    .login-header h2 {
        color: white;
        font-weight: 700;
        margin-bottom: 0.5rem;
        font-size: 2rem;
        text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        animation: glow 2s ease-in-out infinite alternate;
    }

    .login-header p {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        font-size: 1rem;
        font-weight: 500;
    }

    .login-logo {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem auto;
        color: white;
        font-size: 2rem;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        animation: float 3s ease-in-out infinite;
    }

    .form-group {
        margin-bottom: 1.5rem;
        position: relative;
    }

    .form-label {
        font-weight: 600;
        color: white;
        margin-bottom: 0.75rem;
        font-size: 1rem;
        text-shadow: 0 1px 3px rgba(0,0,0,0.3);
    }

    .form-control {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 15px;
        padding: 1rem 1.25rem;
        font-size: 1rem;
        color: white;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
        transform: translateY(-2px);
    }

    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .btn-login {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        padding: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
        width: 100%;
        color: white;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        margin-top: 1rem;
    }

    .btn-login::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: all 0.5s ease;
    }

    .btn-login:hover::before {
        left: 100%;
    }

    .btn-login:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    }

    .login-footer {
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
    }

    .floating-shapes {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: -1;
    }

    .shape {
        position: absolute;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        animation: float-shapes 15s infinite linear;
    }

    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes slideUp {
        0% {
            opacity: 0;
            transform: translateY(50px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes glow {
        0% { text-shadow: 0 2px 10px rgba(0,0,0,0.3); }
        100% { text-shadow: 0 2px 20px rgba(102, 126, 234, 0.5); }
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    @keyframes float-shapes {
        0% {
            transform: translateY(100vh) rotate(0deg);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        90% {
            opacity: 1;
        }
        100% {
            transform: translateY(-100px) rotate(360deg);
            opacity: 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="floating-shapes">
        <div class="shape" style="width: 60px; height: 60px; left: 10%; animation-delay: 0s;"></div>
        <div class="shape" style="width: 80px; height: 80px; left: 20%; animation-delay: 2s;"></div>
        <div class="shape" style="width: 40px; height: 40px; left: 30%; animation-delay: 4s;"></div>
        <div class="shape" style="width: 100px; height: 100px; left: 40%; animation-delay: 6s;"></div>
        <div class="shape" style="width: 50px; height: 50px; left: 50%; animation-delay: 8s;"></div>
        <div class="shape" style="width: 70px; height: 70px; left: 60%; animation-delay: 10s;"></div>
        <div class="shape" style="width: 90px; height: 90px; left: 70%; animation-delay: 12s;"></div>
        <div class="shape" style="width: 30px; height: 30px; left: 80%; animation-delay: 14s;"></div>
        <div class="shape" style="width: 110px; height: 110px; left: 90%; animation-delay: 16s;"></div>
    </div>

    <div class="login-card">
        <div class="login-header">
            <div class="login-logo">
                <i class="fas fa-file-alt"></i>
            </div>
            <h2>مرحباً بك</h2>
            <p>نظام إدارة الصادرة والواردة</p>
        </div>

        <form method="post" id="loginForm">
            {% csrf_token %}

            <div class="form-group">
                <label for="id_username" class="form-label">
                    <i class="fas fa-user me-2"></i>
                    اسم المستخدم
                </label>
                <input type="text" class="form-control" id="id_username" name="username"
                       placeholder="أدخل اسم المستخدم" required autocomplete="username">
            </div>

            <div class="form-group">
                <label for="id_password" class="form-label">
                    <i class="fas fa-lock me-2"></i>
                    كلمة المرور
                </label>
                <input type="password" class="form-control" id="id_password" name="password"
                       placeholder="أدخل كلمة المرور" required autocomplete="current-password">
            </div>

            <button type="submit" class="btn btn-login" id="loginBtn">
                <i class="fas fa-sign-in-alt me-2"></i>
                <span>دخول</span>
            </button>
        </form>

        <div class="login-footer">
            <p>© 2025 نظام إدارة الصادرة والواردة</p>
            <small>جميع الحقوق محفوظة</small>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تركيز على حقل اسم المستخدم
        document.getElementById('id_username').focus();

        // تأثيرات النموذج
        const form = document.getElementById('loginForm');
        const inputs = document.querySelectorAll('.form-control');
        const loginBtn = document.getElementById('loginBtn');

        // تأثيرات الحقول
        inputs.forEach(function(input) {
            input.addEventListener('focus', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 0 20px rgba(102, 126, 234, 0.5)';
            });

            input.addEventListener('blur', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });

            // تأثير الكتابة
            input.addEventListener('input', function() {
                if (this.value.length > 0) {
                    this.style.borderColor = 'rgba(102, 126, 234, 0.8)';
                } else {
                    this.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                }
            });
        });

        // تأثير إرسال النموذج
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // تأثير التحميل
            loginBtn.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center;">
                    <div style="width: 20px; height: 20px; border: 2px solid rgba(255,255,255,0.3);
                                border-top: 2px solid white; border-radius: 50%;
                                animation: spin 1s linear infinite; margin-left: 0.5rem;"></div>
                    <span>جاري التحقق...</span>
                </div>
            `;

            loginBtn.disabled = true;

            // محاكاة التحقق
            setTimeout(() => {
                // إرسال النموذج الفعلي
                form.submit();
            }, 1500);
        });

        // تأثير الجسيمات المتحركة
        createParticles();

        // تأثير الموجات
        createWaves();
    });

    // إنشاء جسيمات متحركة
    function createParticles() {
        const container = document.querySelector('.login-container');

        for (let i = 0; i < 50; i++) {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: absolute;
                width: ${Math.random() * 4 + 1}px;
                height: ${Math.random() * 4 + 1}px;
                background: rgba(255, 255, 255, ${Math.random() * 0.5 + 0.2});
                border-radius: 50%;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: twinkle ${Math.random() * 3 + 2}s infinite;
                pointer-events: none;
            `;
            container.appendChild(particle);
        }
    }

    // إنشاء تأثير الموجات
    function createWaves() {
        const waves = document.createElement('div');
        waves.style.cssText = `
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background: linear-gradient(0deg, rgba(102, 126, 234, 0.1) 0%, transparent 100%);
            overflow: hidden;
            pointer-events: none;
        `;

        for (let i = 0; i < 3; i++) {
            const wave = document.createElement('div');
            wave.style.cssText = `
                position: absolute;
                bottom: 0;
                left: 0;
                width: 200%;
                height: 100%;
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120'%3E%3Cpath d='M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z' fill='rgba(255,255,255,0.1)'%3E%3C/path%3E%3C/svg%3E") repeat-x;
                animation: wave ${6 + i * 2}s ease-in-out infinite;
                animation-delay: ${i * 0.5}s;
                opacity: ${0.3 - i * 0.1};
            `;
            waves.appendChild(wave);
        }

        document.querySelector('.login-container').appendChild(waves);
    }

    // إضافة CSS للحركات
    const style = document.createElement('style');
    style.textContent = `
        @keyframes twinkle {
            0%, 100% { opacity: 0; transform: scale(0); }
            50% { opacity: 1; transform: scale(1); }
        }

        @keyframes wave {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(-50%); }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);
</script>
{% endblock %}

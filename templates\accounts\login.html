{% extends 'base.html' %}

{% block title %}تسجيل الدخول - نظام إدارة الصادرة والواردة{% endblock %}

{% block extra_css %}
<style>
    .login-container {
        min-height: 100vh;
        background-color: #ecf0f1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 0;
    }

    .login-card {
        background: white;
        border-radius: 8px;
        padding: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 400px;
        border: 1px solid #e0e0e0;
    }

    .login-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .login-header h2 {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 1.5rem;
    }

    .login-header p {
        color: #7f8c8d;
        margin: 0;
        font-size: 14px;
    }

    .login-logo {
        width: 60px;
        height: 60px;
        background-color: #3498db;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem auto;
        color: white;
        font-size: 1.5rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        font-weight: 500;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        font-size: 14px;
    }

    .form-control {
        border: 1px solid #bdc3c7;
        border-radius: 6px;
        padding: 0.75rem;
        font-size: 14px;
    }

    .form-control:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .btn-login {
        background-color: #3498db;
        border: none;
        border-radius: 6px;
        padding: 0.75rem;
        font-size: 14px;
        font-weight: 500;
        width: 100%;
        color: white;
    }

    .btn-login:hover {
        background-color: #2980b9;
    }

    .login-footer {
        text-align: center;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid #ecf0f1;
        color: #7f8c8d;
        font-size: 12px;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <div class="login-logo">
                <i class="fas fa-file-alt"></i>
            </div>
            <h2>تسجيل الدخول</h2>
            <p>نظام إدارة الصادرة والواردة</p>
        </div>

        <form method="post">
            {% csrf_token %}

            <div class="form-group">
                <label for="id_username" class="form-label">اسم المستخدم</label>
                <input type="text" class="form-control" id="id_username" name="username"
                       placeholder="أدخل اسم المستخدم" required>
            </div>

            <div class="form-group">
                <label for="id_password" class="form-label">كلمة المرور</label>
                <input type="password" class="form-control" id="id_password" name="password"
                       placeholder="أدخل كلمة المرور" required>
            </div>

            <button type="submit" class="btn btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>
                دخول
            </button>
        </form>

        <div class="login-footer">
            <p>© 2025 نظام إدارة الصادرة والواردة</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تركيز على حقل اسم المستخدم عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('id_username').focus();
    });
    
    // إضافة تأثيرات بصرية للنموذج
    document.querySelectorAll('.form-control').forEach(function(input) {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
    });
</script>
{% endblock %}

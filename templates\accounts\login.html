{% extends 'base.html' %}

{% block title %}تسجيل الدخول - نظام إدارة الصادرة والواردة{% endblock %}

{% block extra_css %}
<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .login-card {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 450px;
    }
    
    .login-header {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .login-header h2 {
        color: #333;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .login-header p {
        color: #666;
        margin: 0;
    }
    
    .login-logo {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem auto;
        color: white;
        font-size: 2rem;
    }
    
    .form-floating {
        margin-bottom: 1.5rem;
    }
    
    .form-floating .form-control {
        border: 2px solid #e9ecef;
        border-radius: 15px;
        height: 60px;
        font-size: 1rem;
    }
    
    .form-floating .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .form-floating label {
        color: #666;
        font-weight: 500;
    }
    
    .btn-login {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        height: 60px;
        font-size: 1.1rem;
        font-weight: 600;
        width: 100%;
        transition: all 0.3s ease;
    }
    
    .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }
    
    .login-footer {
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #e9ecef;
        color: #666;
        font-size: 0.9rem;
    }
    
    .system-info {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 1.5rem;
        margin-top: 2rem;
        color: white;
        text-align: center;
    }
    
    .system-info h5 {
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .system-info ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .system-info li {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }
    
    .system-info i {
        margin-left: 0.5rem;
        width: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="login-card">
                    <div class="login-header">
                        <div class="login-logo">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <h2>تسجيل الدخول</h2>
                        <p>نظام إدارة الصادرة والواردة</p>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="form-floating">
                            <input type="text" class="form-control" id="id_username" name="username" 
                                   placeholder="اسم المستخدم" required>
                            <label for="id_username">اسم المستخدم</label>
                        </div>
                        
                        <div class="form-floating">
                            <input type="password" class="form-control" id="id_password" name="password" 
                                   placeholder="كلمة المرور" required>
                            <label for="id_password">كلمة المرور</label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-login">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            دخول
                        </button>
                    </form>
                    
                    <div class="login-footer">
                        <p>© 2025 نظام إدارة الصادرة والواردة</p>
                        <small>جميع الحقوق محفوظة</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="system-info">
                    <h5>مميزات النظام</h5>
                    <ul>
                        <li><i class="fas fa-inbox"></i>إدارة الكتب الواردة</li>
                        <li><i class="fas fa-paper-plane"></i>إدارة الكتب الصادرة</li>
                        <li><i class="fas fa-exchange-alt"></i>التحويلات الداخلية</li>
                        <li><i class="fas fa-archive"></i>الأرشفة الإلكترونية</li>
                        <li><i class="fas fa-search"></i>البحث المتقدم</li>
                        <li><i class="fas fa-chart-bar"></i>التقارير الشاملة</li>
                        <li><i class="fas fa-users"></i>إدارة الصلاحيات</li>
                        <li><i class="fas fa-shield-alt"></i>الأمان والحماية</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تركيز على حقل اسم المستخدم عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('id_username').focus();
    });
    
    // إضافة تأثيرات بصرية للنموذج
    document.querySelectorAll('.form-control').forEach(function(input) {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
    });
</script>
{% endblock %}

{% extends 'base.html' %}

{% block title %}الملف الشخصي - نظام إدارة الصادرة والواردة{% endblock %}

{% block content %}
<div class="page-header">
    <h1>الملف الشخصي</h1>
    <p>معلومات حسابك وأنشطتك في النظام</p>
</div>

<div class="row">
    <!-- معلومات المستخدم -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    المعلومات الشخصية
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-user-circle fa-5x text-primary"></i>
                </div>
                <h4>{{ user.get_full_arabic_name }}</h4>
                <p class="text-muted">{{ user.position|default:"موظف" }}</p>
                
                <div class="row text-start mt-4">
                    <div class="col-12">
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>اسم المستخدم:</strong></td>
                                <td>{{ user.username }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الموظف:</strong></td>
                                <td>{{ user.employee_id }}</td>
                            </tr>
                            <tr>
                                <td><strong>البريد الإلكتروني:</strong></td>
                                <td>{{ user.email|default:"-" }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهاتف:</strong></td>
                                <td>{{ user.phone|default:"-" }}</td>
                            </tr>
                            <tr>
                                <td><strong>القسم:</strong></td>
                                <td>{{ user.department.name|default:"-" }}</td>
                            </tr>
                            <tr>
                                <td><strong>الدور:</strong></td>
                                <td>
                                    {% if user.role %}
                                        <span class="badge bg-primary">{{ user.role.name }}</span>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ التعيين:</strong></td>
                                <td>{{ user.hire_date|date:"d/m/Y"|default:"-" }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الانضمام:</strong></td>
                                <td>{{ user.date_joined|date:"d/m/Y" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="editProfile()">
                        <i class="fas fa-edit me-1"></i>
                        تعديل المعلومات
                    </button>
                </div>
            </div>
        </div>

        <!-- الصلاحيات -->
        {% if user.role %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-key me-2"></i>
                    الصلاحيات
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if user.role.can_view %}checked{% endif %} disabled>
                            <label class="form-check-label">مشاهدة</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if user.role.can_add %}checked{% endif %} disabled>
                            <label class="form-check-label">إضافة</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if user.role.can_edit %}checked{% endif %} disabled>
                            <label class="form-check-label">تعديل</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if user.role.can_delete %}checked{% endif %} disabled>
                            <label class="form-check-label">حذف</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if user.role.can_approve %}checked{% endif %} disabled>
                            <label class="form-check-label">موافقة</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if user.role.can_transfer %}checked{% endif %} disabled>
                            <label class="form-check-label">تحويل</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if user.role.can_generate_reports %}checked{% endif %} disabled>
                            <label class="form-check-label">تقارير</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if user.role.can_manage_users %}checked{% endif %} disabled>
                            <label class="form-check-label">إدارة المستخدمين</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- الأنشطة والجلسات -->
    <div class="col-lg-8">
        <!-- الجلسات النشطة -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-desktop me-2"></i>
                    الجلسات الأخيرة
                </h5>
            </div>
            <div class="card-body">
                {% if sessions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>عنوان IP</th>
                                    <th>المتصفح</th>
                                    <th>وقت الدخول</th>
                                    <th>وقت الخروج</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for session in sessions %}
                                <tr>
                                    <td>{{ session.ip_address }}</td>
                                    <td>{{ session.user_agent|truncatechars:30 }}</td>
                                    <td>{{ session.login_time|date:"d/m/Y H:i" }}</td>
                                    <td>
                                        {% if session.logout_time %}
                                            {{ session.logout_time|date:"d/m/Y H:i" }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if session.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-secondary">منتهي</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا توجد جلسات</p>
                {% endif %}
            </div>
        </div>

        <!-- سجل الأنشطة -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    سجل الأنشطة الأخيرة
                </h5>
            </div>
            <div class="card-body">
                {% if activities %}
                    <div class="timeline">
                        {% for activity in activities %}
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                {% if activity.activity_type == 'login' %}
                                    <i class="fas fa-sign-in-alt text-success"></i>
                                {% elif activity.activity_type == 'logout' %}
                                    <i class="fas fa-sign-out-alt text-warning"></i>
                                {% elif activity.activity_type == 'create' %}
                                    <i class="fas fa-plus text-primary"></i>
                                {% elif activity.activity_type == 'update' %}
                                    <i class="fas fa-edit text-info"></i>
                                {% elif activity.activity_type == 'delete' %}
                                    <i class="fas fa-trash text-danger"></i>
                                {% else %}
                                    <i class="fas fa-circle text-secondary"></i>
                                {% endif %}
                            </div>
                            <div class="timeline-content">
                                <div class="d-flex justify-content-between">
                                    <strong>{{ activity.description }}</strong>
                                    <small class="text-muted">{{ activity.timestamp|date:"d/m/Y H:i" }}</small>
                                </div>
                                <small class="text-muted">من {{ activity.ip_address }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا توجد أنشطة</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e9ecef;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 0;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: white;
        border: 2px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
    }
    
    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 3px solid #007bff;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    function editProfile() {
        alert('ميزة تعديل الملف الشخصي قيد التطوير');
    }
</script>
{% endblock %}

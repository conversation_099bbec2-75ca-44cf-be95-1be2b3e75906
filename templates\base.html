{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الصادرة والواردة{% endblock %}</title>

    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/custom.css' %}" rel="stylesheet">
    <link href="{% static 'css/advanced.css' %}" rel="stylesheet">
    <link href="{% static 'css/animations.css' %}" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f5f5f5;
            font-size: 14px;
        }

        .navbar {
            background-color: #2c3e50 !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.2rem;
        }

        .sidebar {
            background-color: #34495e;
            min-height: calc(100vh - 56px);
            padding: 1rem 0;
        }

        .sidebar .nav-link {
            color: #bdc3c7;
            padding: 0.8rem 1.5rem;
            margin-bottom: 0.2rem;
            border-radius: 0;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: #3498db;
            border-radius: 0;
        }

        .sidebar .nav-link i {
            margin-left: 0.5rem;
            width: 18px;
            text-align: center;
        }

        .main-content {
            padding: 1.5rem;
            background-color: #f5f5f5;
        }

        .card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }

        .card-header {
            background-color: #3498db;
            color: white;
            border-radius: 8px 8px 0 0 !important;
            font-weight: 500;
            padding: 1rem 1.25rem;
            border-bottom: none;
        }

        .card-body {
            padding: 1.25rem;
        }

        .btn {
            border-radius: 6px;
            font-weight: 500;
            padding: 0.5rem 1rem;
            font-size: 14px;
        }

        .btn-primary {
            background-color: #3498db;
            border-color: #3498db;
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }

        .btn-success {
            background-color: #27ae60;
            border-color: #27ae60;
        }

        .btn-success:hover {
            background-color: #229954;
            border-color: #229954;
        }

        .btn-warning {
            background-color: #f39c12;
            border-color: #f39c12;
        }

        .btn-warning:hover {
            background-color: #e67e22;
            border-color: #e67e22;
        }

        .btn-danger {
            background-color: #e74c3c;
            border-color: #e74c3c;
        }

        .btn-danger:hover {
            background-color: #c0392b;
            border-color: #c0392b;
        }

        .table {
            font-size: 14px;
        }

        .table thead th {
            background-color: #ecf0f1;
            color: #2c3e50;
            border-bottom: 2px solid #bdc3c7;
            font-weight: 600;
            padding: 0.75rem;
        }

        .table tbody td {
            padding: 0.75rem;
            vertical-align: middle;
        }

        .badge {
            font-size: 12px;
            padding: 0.4rem 0.8rem;
            border-radius: 4px;
        }

        .alert {
            border-radius: 6px;
            border: none;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .stats-card {
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #3498db;
            margin-bottom: 0.5rem;
        }

        .stats-label {
            font-size: 14px;
            color: #7f8c8d;
            font-weight: 500;
        }

        .page-title {
            background-color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            border: 1px solid #e0e0e0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .page-title h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .page-title p {
            margin: 0.5rem 0 0 0;
            color: #7f8c8d;
            font-size: 14px;
        }

        .form-control, .form-select {
            border-radius: 6px;
            border: 1px solid #bdc3c7;
            padding: 0.5rem 0.75rem;
            font-size: 14px;
        }

        .form-control:focus, .form-select:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .breadcrumb {
            background-color: transparent;
            padding: 0;
            margin-bottom: 1rem;
            font-size: 14px;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "←";
        }

        .breadcrumb-item a {
            color: #3498db;
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: #7f8c8d;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'dashboard' %}">
                <i class="fas fa-file-alt me-2"></i>
                نظام الصادرة والواردة
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                {{ user.get_full_arabic_name }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                    <i class="fas fa-user-circle me-2"></i>الملف الشخصي
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            {% if user.is_authenticated %}
                <!-- Sidebar -->
                <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                    <div class="position-sticky">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'dashboard' %}">
                                    <i class="fas fa-home"></i>
                                    الرئيسية
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'documents:incoming_list' %}">
                                    <i class="fas fa-inbox"></i>
                                    الكتب الواردة
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'documents:outgoing_list' %}">
                                    <i class="fas fa-paper-plane"></i>
                                    الكتب الصادرة
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="fas fa-exchange-alt"></i>
                                    التحويلات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="fas fa-chart-bar"></i>
                                    التقارير
                                </a>
                            </li>
                            {% if user.is_staff %}
                                <li class="nav-item">
                                    <a class="nav-link" href="/admin/">
                                        <i class="fas fa-cogs"></i>
                                        الإدارة
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                </nav>

                <!-- Main content -->
                <main class="col-md-9 ms-sm-auto col-lg-10 main-content">
            {% else %}
                <main class="col-12 main-content">
            {% endif %}
                
                <!-- Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Content -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Advanced JS -->
    <script src="{% static 'js/advanced.js' %}"></script>

    <!-- Custom JS -->
    <script>
        // تفعيل tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })
        
        // إخفاء الرسائل تلقائياً بعد 5 ثوان
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>

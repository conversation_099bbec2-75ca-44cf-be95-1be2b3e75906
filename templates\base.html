{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الصادرة والواردة{% endblock %}</title>

    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary: #4f46e5;
            --primary-dark: #3730a3;
            --secondary: #64748b;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --light: #f8fafc;
            --dark: #1e293b;
            --border: #e2e8f0;
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --radius: 0.5rem;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background-color: var(--light);
            color: var(--dark);
            line-height: 1.6;
            font-size: 14px;
        }

        /* شريط التنقل */
        .navbar {
            background: white !important;
            border-bottom: 1px solid var(--border);
            box-shadow: var(--shadow);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.25rem;
            color: var(--primary) !important;
        }

        .navbar-nav .nav-link {
            color: var(--secondary) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: var(--radius);
            transition: all 0.2s;
        }

        .navbar-nav .nav-link:hover {
            background-color: var(--light);
            color: var(--primary) !important;
        }

        /* الشريط الجانبي */
        .sidebar {
            background: white;
            border-left: 1px solid var(--border);
            min-height: calc(100vh - 76px);
            padding: 1.5rem 0;
        }

        .sidebar .nav-link {
            color: var(--secondary);
            padding: 0.75rem 1.5rem;
            margin: 0.25rem 1rem;
            border-radius: var(--radius);
            font-weight: 500;
            transition: all 0.2s;
            border: none;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: var(--primary);
            color: white;
            transform: translateX(-2px);
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-left: 0.75rem;
            text-align: center;
        }

        /* المحتوى الرئيسي */
        .main-content {
            padding: 2rem;
            background-color: var(--light);
        }

        /* البطاقات */
        .card {
            background: white;
            border: 1px solid var(--border);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transition: box-shadow 0.2s;
        }

        .card-header {
            background: var(--primary);
            color: white;
            padding: 1rem 1.5rem;
            border-bottom: none;
            font-weight: 600;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* الأزرار */
        .btn {
            font-weight: 500;
            border-radius: var(--radius);
            padding: 0.5rem 1rem;
            border: none;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
        }

        .btn-success {
            background-color: var(--success);
            color: white;
        }

        .btn-warning {
            background-color: var(--warning);
            color: white;
        }

        .btn-danger {
            background-color: var(--danger);
            color: white;
        }

        .btn-outline-primary {
            border: 1px solid var(--primary);
            color: var(--primary);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }

        /* الجداول */
        .table {
            background: white;
            border-radius: var(--radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .table thead th {
            background-color: var(--light);
            border-bottom: 2px solid var(--border);
            font-weight: 600;
            color: var(--dark);
            padding: 1rem;
        }

        .table tbody td {
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: var(--light);
        }

        /* النماذج */
        .form-control, .form-select {
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 0.75rem;
            font-size: 14px;
            transition: all 0.2s;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgb(79 70 229 / 0.1);
        }

        .form-label {
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 0.5rem;
        }

        /* الشارات */
        .badge {
            font-weight: 500;
            padding: 0.375rem 0.75rem;
            border-radius: 9999px;
        }

        /* عنوان الصفحة */
        .page-title {
            background: white;
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .page-title h1 {
            color: var(--dark);
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .page-title p {
            color: var(--secondary);
            margin: 0;
        }

        /* بطاقات الإحصائيات */
        .stats-card {
            background: white;
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1.5rem;
            text-align: center;
            box-shadow: var(--shadow);
            transition: all 0.2s;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .stats-label {
            color: var(--secondary);
            font-weight: 500;
        }

        /* التنبيهات */
        .alert {
            border: none;
            border-radius: var(--radius);
            padding: 1rem;
            margin-bottom: 1rem;
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .main-content { padding: 1rem; }
            .page-title { padding: 1rem; margin-bottom: 1rem; }
            .card-body { padding: 1rem; }
            .stats-card { margin-bottom: 1rem; }
        }

        /* تحسينات إضافية */
        .text-muted { color: var(--secondary) !important; }
        .border { border-color: var(--border) !important; }
        .bg-light { background-color: var(--light) !important; }

        /* تأثيرات بسيطة */
        .fade-in { animation: fadeIn 0.5s ease-in; }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>

<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'dashboard' %}">
                <i class="fas fa-file-alt me-2"></i>
                نظام الصادرة والواردة
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                {{ user.get_full_arabic_name }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                    <i class="fas fa-user-circle me-2"></i>الملف الشخصي
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid">
        <div class="row">
            {% if user.is_authenticated %}
                <!-- الشريط الجانبي -->
                <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                    <div class="position-sticky">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'dashboard' %}">
                                    <i class="fas fa-home"></i>
                                    الرئيسية
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'documents:incoming_list' %}">
                                    <i class="fas fa-inbox"></i>
                                    الكتب الواردة
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'documents:outgoing_list' %}">
                                    <i class="fas fa-paper-plane"></i>
                                    الكتب الصادرة
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="fas fa-exchange-alt"></i>
                                    التحويلات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="fas fa-chart-bar"></i>
                                    التقارير
                                </a>
                            </li>
                            {% if user.is_staff %}
                                <li class="nav-item">
                                    <a class="nav-link" href="/admin/">
                                        <i class="fas fa-cogs"></i>
                                        الإدارة
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                </nav>

                <!-- المحتوى الرئيسي -->
                <main class="col-md-9 ms-sm-auto col-lg-10 main-content">
            {% else %}
                <main class="col-12 main-content">
            {% endif %}

                <!-- عرض الرسائل -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- محتوى الصفحة -->
                {% block content %}{% endblock %}

            </main>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        // تفعيل التأثيرات البسيطة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثير fade-in للعناصر
            const elements = document.querySelectorAll('.card, .page-title, .stats-card');
            elements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    el.style.transition = 'all 0.5s ease';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // تفعيل الروابط النشطة في الشريط الجانبي
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
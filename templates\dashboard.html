{% extends 'base.html' %}

{% block title %}الرئيسية - نظام إدارة الصادرة والواردة{% endblock %}

{% block content %}
<div class="page-title">
    <div class="d-flex align-items-center justify-content-between">
        <div>
            <h1 class="typing-text">مرحباً {{ user.get_full_arabic_name }}</h1>
            <p>نظرة عامة على النشاط اليومي</p>
        </div>
        <div class="welcome-icon">
            <i class="fas fa-chart-line fa-3x"></i>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card animate-card" data-aos="fade-up" data-aos-delay="100">
            <div class="stats-icon">
                <i class="fas fa-inbox"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number counter" data-target="{{ total_incoming }}">0</div>
                <div class="stats-label">الكتب الواردة</div>
            </div>
            <div class="stats-progress">
                <div class="progress-bar" style="width: 75%;"></div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card animate-card" data-aos="fade-up" data-aos-delay="200">
            <div class="stats-icon">
                <i class="fas fa-paper-plane"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number counter" data-target="{{ total_outgoing }}">0</div>
                <div class="stats-label">الكتب الصادرة</div>
            </div>
            <div class="stats-progress">
                <div class="progress-bar" style="width: 60%;"></div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card animate-card" data-aos="fade-up" data-aos-delay="300">
            <div class="stats-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number counter" data-target="{{ pending_incoming }}">0</div>
                <div class="stats-label">الكتب المعلقة</div>
            </div>
            <div class="stats-progress">
                <div class="progress-bar" style="width: 40%;"></div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card animate-card" data-aos="fade-up" data-aos-delay="400">
            <div class="stats-icon">
                <i class="fas fa-exchange-alt"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number counter" data-target="{{ pending_transfers.count }}">0</div>
                <div class="stats-label">التحويلات المعلقة</div>
            </div>
            <div class="stats-progress">
                <div class="progress-bar" style="width: 30%;"></div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الكتب الواردة الحديثة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-inbox me-2"></i>
                    الكتب الواردة الحديثة
                </h5>
                <a href="{% url 'documents:incoming_list' %}" class="btn btn-light btn-sm">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_incoming %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الورود</th>
                                    <th>الجهة المرسلة</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for doc in recent_incoming %}
                                <tr>
                                    <td>
                                        <a href="{% url 'documents:incoming_detail' doc.pk %}" class="text-decoration-none">
                                            {{ doc.incoming_number }}
                                        </a>
                                    </td>
                                    <td>{{ doc.sender_entity|truncatechars:30 }}</td>
                                    <td>
                                        <span class="badge" style="background-color: {{ doc.status.color }}">
                                            {{ doc.status.name }}
                                        </span>
                                    </td>
                                    <td>{{ doc.incoming_date|date:"d/m/Y" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد كتب واردة حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- الكتب الصادرة الحديثة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-paper-plane me-2"></i>
                    الكتب الصادرة الحديثة
                </h5>
                <a href="{% url 'documents:outgoing_list' %}" class="btn btn-light btn-sm">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_outgoing %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الكتاب</th>
                                    <th>الجهة المرسل إليها</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for doc in recent_outgoing %}
                                <tr>
                                    <td>
                                        <a href="{% url 'documents:outgoing_detail' doc.pk %}" class="text-decoration-none">
                                            {{ doc.document_number }}
                                        </a>
                                    </td>
                                    <td>{{ doc.recipient_entity|truncatechars:30 }}</td>
                                    <td>
                                        <span class="badge" style="background-color: {{ doc.status.color }}">
                                            {{ doc.status.name }}
                                        </span>
                                    </td>
                                    <td>{{ doc.document_date|date:"d/m/Y" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد كتب صادرة حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- التحويلات المعلقة -->
{% if pending_transfers %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>
                    التحويلات المعلقة
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الكتاب</th>
                                <th>من القسم</th>
                                <th>إلى القسم</th>
                                <th>المحول</th>
                                <th>تاريخ التحويل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transfer in pending_transfers %}
                            <tr>
                                <td>
                                    <a href="{% url 'documents:incoming_detail' transfer.incoming_document.pk %}" 
                                       class="text-decoration-none">
                                        {{ transfer.incoming_document.incoming_number }}
                                    </a>
                                </td>
                                <td>{{ transfer.from_department.name }}</td>
                                <td>{{ transfer.to_department.name }}</td>
                                <td>{{ transfer.from_user.get_full_arabic_name }}</td>
                                <td>{{ transfer.transfer_date|date:"d/m/Y H:i" }}</td>
                                <td>
                                    <button class="btn btn-sm btn-success" onclick="receiveTransfer({{ transfer.pk }})">
                                        <i class="fas fa-check me-1"></i>
                                        استلام
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- روابط سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-plus fa-2x mb-2"></i>
                            <span>إضافة كتاب وارد</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-edit fa-2x mb-2"></i>
                            <span>إنشاء كتاب صادر</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-search fa-2x mb-2"></i>
                            <span>البحث المتقدم</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <span>التقارير</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .welcome-icon {
        opacity: 0.7;
        animation: pulse 2s infinite;
    }

    .stats-card {
        position: relative;
        padding: 2rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        overflow: hidden;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
        backdrop-filter: blur(10px);
        font-size: 1.5rem;
        color: white;
        flex-shrink: 0;
    }

    .stats-content {
        flex: 1;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: white;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 10px rgba(0,0,0,0.3);
    }

    .stats-label {
        color: rgba(255,255,255,0.9);
        font-weight: 500;
        font-size: 0.9rem;
    }

    .stats-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: rgba(255,255,255,0.2);
    }

    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #fff, rgba(255,255,255,0.8));
        transition: width 2s ease;
        animation: shimmer 2s infinite;
    }

    .animate-card {
        opacity: 0;
        transform: translateY(30px);
        animation: slideInUp 0.8s ease forwards;
    }

    .typing-text {
        overflow: hidden;
        border-right: 2px solid;
        white-space: nowrap;
        animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    @keyframes slideInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes shimmer {
        0% { background-position: -200px 0; }
        100% { background-position: 200px 0; }
    }

    @keyframes typing {
        from { width: 0; }
        to { width: 100%; }
    }

    @keyframes blink-caret {
        from, to { border-color: transparent; }
        50% { border-color: currentColor; }
    }

    .chart-container {
        position: relative;
        height: 300px;
        background: rgba(255,255,255,0.1);
        border-radius: 15px;
        padding: 1rem;
        backdrop-filter: blur(10px);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير العد التصاعدي للأرقام
        animateCounters();

        // تأثير ظهور البطاقات بالتتابع
        animateCards();

        // تحديث الوقت الحالي
        updateCurrentTime();

        // إضافة تأثيرات تفاعلية
        addInteractiveEffects();
    });

    function animateCounters() {
        const counters = document.querySelectorAll('.counter');

        counters.forEach((counter, index) => {
            const target = parseInt(counter.getAttribute('data-target'));
            const duration = 2000; // 2 ثانية
            const step = target / (duration / 16); // 60 FPS
            let current = 0;

            const timer = setInterval(() => {
                current += step;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                counter.textContent = Math.floor(current);
            }, 16);

            // تأخير بدء العد حسب ترتيب البطاقة
            setTimeout(() => {
                // بدء العد
            }, index * 200);
        });
    }

    function animateCards() {
        const cards = document.querySelectorAll('.animate-card');

        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate');
            }, index * 100);
        });
    }

    function updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleString('ar-EG', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        // إضافة الوقت إلى الصفحة إذا لم يكن موجوداً
        if (!document.getElementById('current-time')) {
            const timeElement = document.createElement('div');
            timeElement.id = 'current-time';
            timeElement.style.cssText = `
                position: fixed;
                top: 70px;
                right: 20px;
                background: rgba(255,255,255,0.2);
                backdrop-filter: blur(10px);
                padding: 0.5rem 1rem;
                border-radius: 25px;
                color: white;
                font-size: 0.9rem;
                font-weight: 500;
                z-index: 1000;
                border: 1px solid rgba(255,255,255,0.3);
            `;
            timeElement.textContent = timeString;
            document.body.appendChild(timeElement);
        }

        // تحديث كل دقيقة
        setTimeout(updateCurrentTime, 60000);
    }

    function addInteractiveEffects() {
        // تأثير التحويم على بطاقات الإحصائيات
        const statsCards = document.querySelectorAll('.stats-card');

        statsCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.05)';
                this.style.boxShadow = '0 20px 40px rgba(0,0,0,0.3)';

                // تأثير النبض على الأيقونة
                const icon = this.querySelector('.stats-icon');
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '0 8px 32px 0 rgba(31, 38, 135, 0.37)';

                const icon = this.querySelector('.stats-icon');
                icon.style.transform = 'scale(1) rotate(0deg)';
            });
        });

        // تأثير النقر على البطاقات
        statsCards.forEach(card => {
            card.addEventListener('click', function() {
                // تأثير الموجة
                const ripple = document.createElement('div');
                ripple.style.cssText = `
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255,255,255,0.6);
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                    pointer-events: none;
                    width: 100px;
                    height: 100px;
                    left: 50%;
                    top: 50%;
                    margin-left: -50px;
                    margin-top: -50px;
                `;

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);

                // إظهار إشعار
                showNotification('تم النقر على بطاقة الإحصائيات', 'info', 2000);
            });
        });
    }

    function receiveTransfer(transferId) {
        if (confirm('هل أنت متأكد من استلام هذا التحويل؟')) {
            // تأثير التحميل
            showNotification('جاري معالجة التحويل...', 'info', 2000);

            setTimeout(() => {
                showNotification('تم استلام التحويل بنجاح', 'success', 3000);
                // location.reload();
            }, 1500);
        }
    }

    // تحديث تلقائي للبيانات
    setInterval(() => {
        // محاكاة تحديث البيانات
        const counters = document.querySelectorAll('.counter');
        counters.forEach(counter => {
            const current = parseInt(counter.textContent);
            const change = Math.floor(Math.random() * 3) - 1; // -1, 0, أو 1
            const newValue = Math.max(0, current + change);

            if (change !== 0) {
                counter.style.transform = 'scale(1.1)';
                counter.textContent = newValue;

                setTimeout(() => {
                    counter.style.transform = 'scale(1)';
                }, 200);
            }
        });
    }, 30000); // كل 30 ثانية

    // إضافة CSS للتأثيرات
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .stats-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }

        .stats-icon {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .counter {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
    `;
    document.head.appendChild(style);
</script>
{% endblock %}

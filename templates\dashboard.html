{% extends 'base.html' %}

{% block title %}الرئيسية - نظام إدارة الصادرة والواردة{% endblock %}

{% block content %}
<div class="page-header">
    <h1>مرحباً {{ user.get_full_arabic_name }}</h1>
    <p>نظرة عامة على النشاط اليومي</p>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ total_incoming }}</div>
            <div class="stats-label">
                <i class="fas fa-inbox me-2"></i>
                إجمالي الكتب الواردة
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ total_outgoing }}</div>
            <div class="stats-label">
                <i class="fas fa-paper-plane me-2"></i>
                إجمالي الكتب الصادرة
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ pending_incoming }}</div>
            <div class="stats-label">
                <i class="fas fa-clock me-2"></i>
                الكتب المعلقة
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number">{{ pending_transfers.count }}</div>
            <div class="stats-label">
                <i class="fas fa-exchange-alt me-2"></i>
                التحويلات المعلقة
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الكتب الواردة الحديثة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-inbox me-2"></i>
                    الكتب الواردة الحديثة
                </h5>
                <a href="{% url 'documents:incoming_list' %}" class="btn btn-light btn-sm">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_incoming %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الورود</th>
                                    <th>الجهة المرسلة</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for doc in recent_incoming %}
                                <tr>
                                    <td>
                                        <a href="{% url 'documents:incoming_detail' doc.pk %}" class="text-decoration-none">
                                            {{ doc.incoming_number }}
                                        </a>
                                    </td>
                                    <td>{{ doc.sender_entity|truncatechars:30 }}</td>
                                    <td>
                                        <span class="badge" style="background-color: {{ doc.status.color }}">
                                            {{ doc.status.name }}
                                        </span>
                                    </td>
                                    <td>{{ doc.incoming_date|date:"d/m/Y" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد كتب واردة حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- الكتب الصادرة الحديثة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-paper-plane me-2"></i>
                    الكتب الصادرة الحديثة
                </h5>
                <a href="{% url 'documents:outgoing_list' %}" class="btn btn-light btn-sm">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_outgoing %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الكتاب</th>
                                    <th>الجهة المرسل إليها</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for doc in recent_outgoing %}
                                <tr>
                                    <td>
                                        <a href="{% url 'documents:outgoing_detail' doc.pk %}" class="text-decoration-none">
                                            {{ doc.document_number }}
                                        </a>
                                    </td>
                                    <td>{{ doc.recipient_entity|truncatechars:30 }}</td>
                                    <td>
                                        <span class="badge" style="background-color: {{ doc.status.color }}">
                                            {{ doc.status.name }}
                                        </span>
                                    </td>
                                    <td>{{ doc.document_date|date:"d/m/Y" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد كتب صادرة حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- التحويلات المعلقة -->
{% if pending_transfers %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>
                    التحويلات المعلقة
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الكتاب</th>
                                <th>من القسم</th>
                                <th>إلى القسم</th>
                                <th>المحول</th>
                                <th>تاريخ التحويل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transfer in pending_transfers %}
                            <tr>
                                <td>
                                    <a href="{% url 'documents:incoming_detail' transfer.incoming_document.pk %}" 
                                       class="text-decoration-none">
                                        {{ transfer.incoming_document.incoming_number }}
                                    </a>
                                </td>
                                <td>{{ transfer.from_department.name }}</td>
                                <td>{{ transfer.to_department.name }}</td>
                                <td>{{ transfer.from_user.get_full_arabic_name }}</td>
                                <td>{{ transfer.transfer_date|date:"d/m/Y H:i" }}</td>
                                <td>
                                    <button class="btn btn-sm btn-success" onclick="receiveTransfer({{ transfer.pk }})">
                                        <i class="fas fa-check me-1"></i>
                                        استلام
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- روابط سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-plus fa-2x mb-2"></i>
                            <span>إضافة كتاب وارد</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-edit fa-2x mb-2"></i>
                            <span>إنشاء كتاب صادر</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-search fa-2x mb-2"></i>
                            <span>البحث المتقدم</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <span>التقارير</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function receiveTransfer(transferId) {
        if (confirm('هل أنت متأكد من استلام هذا التحويل؟')) {
            // هنا يمكن إضافة AJAX call لاستلام التحويل
            alert('تم استلام التحويل بنجاح');
            location.reload();
        }
    }
    
    // تحديث الصفحة كل 5 دقائق للحصول على آخر البيانات
    setTimeout(function() {
        location.reload();
    }, 300000); // 5 دقائق
</script>
{% endblock %}

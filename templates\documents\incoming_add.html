{% extends 'base.html' %}

{% block title %}إضافة كتاب وارد - نظام إدارة الصادرة والواردة{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:incoming_list' %}">الكتب الواردة</a></li>
        <li class="breadcrumb-item active">إضافة كتاب وارد</li>
    </ol>
</nav>

<div class="page-title">
    <h1>إضافة كتاب وارد جديد</h1>
    <p>إدخال بيانات كتاب وارد جديد</p>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    بيانات الكتاب الوارد
                </h6>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="document_number" class="form-label">رقم الكتاب</label>
                            <input type="text" class="form-control" id="document_number" name="document_number" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="document_date" class="form-label">تاريخ الكتاب</label>
                            <input type="date" class="form-control" id="document_date" name="document_date" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="incoming_number" class="form-label">رقم الورود</label>
                            <input type="text" class="form-control" id="incoming_number" name="incoming_number" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="sender_entity" class="form-label">الجهة المرسلة</label>
                            <input type="text" class="form-control" id="sender_entity" name="sender_entity" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subject" class="form-label">موضوع الكتاب</label>
                        <textarea class="form-control" id="subject" name="subject" rows="3" required></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="document_type" class="form-label">نوع الكتاب</label>
                            <select class="form-select" id="document_type" name="document_type" required>
                                <option value="">اختر النوع</option>
                                <!-- سيتم ملؤها من قاعدة البيانات -->
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="priority" class="form-label">الأولوية</label>
                            <select class="form-select" id="priority" name="priority" required>
                                <option value="low">عادي</option>
                                <option value="medium" selected>مهم</option>
                                <option value="high">عاجل</option>
                                <option value="urgent">عاجل جداً</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="assigned_to_department" class="form-label">القسم المحول إليه</label>
                            <select class="form-select" id="assigned_to_department" name="assigned_to_department" required>
                                <option value="">اختر القسم</option>
                                <!-- سيتم ملؤها من قاعدة البيانات -->
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="document_file" class="form-label">ملف الكتاب</label>
                            <input type="file" class="form-control" id="document_file" name="document_file" 
                                   accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                            <div class="form-text">الملفات المدعومة: PDF, DOC, DOCX, JPG, PNG</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="deadline" class="form-label">الموعد النهائي (اختياري)</label>
                            <input type="date" class="form-control" id="deadline" name="deadline">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات (اختياري)</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ الكتاب
                        </button>
                        <a href="{% url 'documents:incoming_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    إرشادات
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6>تعليمات مهمة:</h6>
                    <ul class="mb-0">
                        <li>تأكد من صحة رقم الكتاب وتاريخه</li>
                        <li>اختر القسم المناسب للتحويل</li>
                        <li>حدد الأولوية بدقة</li>
                        <li>ارفق نسخة من الكتاب إن أمكن</li>
                    </ul>
                </div>
                
                <div class="mt-3">
                    <h6>الحقول المطلوبة:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-1"></i> رقم الكتاب</li>
                        <li><i class="fas fa-check text-success me-1"></i> تاريخ الكتاب</li>
                        <li><i class="fas fa-check text-success me-1"></i> رقم الورود</li>
                        <li><i class="fas fa-check text-success me-1"></i> الجهة المرسلة</li>
                        <li><i class="fas fa-check text-success me-1"></i> موضوع الكتاب</li>
                        <li><i class="fas fa-check text-success me-1"></i> نوع الكتاب</li>
                        <li><i class="fas fa-check text-success me-1"></i> القسم المحول إليه</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تعيين التاريخ الحالي كافتراضي
    document.addEventListener('DOMContentLoaded', function() {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('document_date').value = today;
        
        // توليد رقم ورود تلقائي (مبسط)
        const year = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 1000) + 1;
        document.getElementById('incoming_number').value = `و/${year}/${randomNum.toString().padStart(3, '0')}`;
    });
    
    // التحقق من صحة النموذج
    document.querySelector('form').addEventListener('submit', function(e) {
        const requiredFields = ['document_number', 'document_date', 'incoming_number', 'sender_entity', 'subject'];
        let isValid = true;
        
        requiredFields.forEach(function(fieldId) {
            const field = document.getElementById(fieldId);
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });
</script>
{% endblock %}

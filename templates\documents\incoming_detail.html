{% extends 'base.html' %}

{% block title %}تفاصيل الكتاب الوارد {{ document.incoming_number }} - نظام إدارة الصادرة والواردة{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:incoming_list' %}">الكتب الواردة</a></li>
        <li class="breadcrumb-item active">{{ document.incoming_number }}</li>
    </ol>
</nav>

<div class="page-title">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>تفاصيل الكتاب الوارد</h1>
            <p>رقم الورود: {{ document.incoming_number }}</p>
        </div>
        <div>
            <div class="btn-group" role="group">
                <button class="btn btn-success btn-sm" onclick="transferDocument()">
                    <i class="fas fa-share me-1"></i>
                    تحويل
                </button>
                <button class="btn btn-info btn-sm" onclick="addComment()">
                    <i class="fas fa-comment me-1"></i>
                    تعليق
                </button>
                <button class="btn btn-warning btn-sm" onclick="updateStatus()">
                    <i class="fas fa-edit me-1"></i>
                    تحديث
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="printDocument()">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- معلومات الكتاب الأساسية -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    معلومات الكتاب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم الورود:</strong></td>
                                <td>{{ document.incoming_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الكتاب:</strong></td>
                                <td>{{ document.document_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الكتاب:</strong></td>
                                <td>{{ document.document_date|date:"d/m/Y" }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الورود:</strong></td>
                                <td>{{ document.incoming_date|date:"d/m/Y H:i" }}</td>
                            </tr>
                            <tr>
                                <td><strong>الجهة المرسلة:</strong></td>
                                <td>{{ document.sender_entity }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>نوع الكتاب:</strong></td>
                                <td><span class="badge bg-info">{{ document.document_type.name }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <span class="badge" style="background-color: {{ document.status.color }}">
                                        {{ document.status.name }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الأولوية:</strong></td>
                                <td>
                                    {% if document.priority == 'urgent' %}
                                        <span class="badge bg-danger">عاجل جداً</span>
                                    {% elif document.priority == 'high' %}
                                        <span class="badge bg-warning">عاجل</span>
                                    {% elif document.priority == 'medium' %}
                                        <span class="badge bg-primary">مهم</span>
                                    {% else %}
                                        <span class="badge bg-secondary">عادي</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>المستلم:</strong></td>
                                <td>{{ document.received_by.get_full_arabic_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>القسم المحول إليه:</strong></td>
                                <td>{{ document.assigned_to_department.name }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6><strong>موضوع الكتاب:</strong></h6>
                        <p class="bg-light p-3 rounded">{{ document.subject }}</p>
                    </div>
                </div>
                
                {% if document.notes %}
                <div class="row">
                    <div class="col-12">
                        <h6><strong>ملاحظات:</strong></h6>
                        <p class="bg-light p-3 rounded">{{ document.notes }}</p>
                    </div>
                </div>
                {% endif %}
                
                {% if document.deadline %}
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-warning">
                            <i class="fas fa-clock me-2"></i>
                            <strong>الموعد النهائي:</strong> {{ document.deadline|date:"d/m/Y" }}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- الملف المرفق -->
        {% if document.document_file %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-paperclip me-2"></i>
                    الملف المرفق
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <i class="fas fa-file fa-2x text-primary me-3"></i>
                    <div>
                        <h6 class="mb-1">{{ document.document_file.name|slice:"20:" }}</h6>
                        <small class="text-muted">حجم الملف: {{ document.document_file.size|filesizeformat }}</small>
                    </div>
                    <div class="ms-auto">
                        <a href="{{ document.document_file.url }}" class="btn btn-primary" target="_blank">
                            <i class="fas fa-download me-1"></i>
                            تحميل
                        </a>
                        <button class="btn btn-outline-primary" onclick="viewFile('{{ document.document_file.url }}')">
                            <i class="fas fa-eye me-1"></i>
                            عرض
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- التحويلات -->
        {% if transfers %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>
                    سجل التحويلات
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>من القسم</th>
                                <th>إلى القسم</th>
                                <th>المحول</th>
                                <th>تاريخ التحويل</th>
                                <th>الحالة</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transfer in transfers %}
                            <tr>
                                <td>{{ transfer.from_department.name }}</td>
                                <td>{{ transfer.to_department.name }}</td>
                                <td>{{ transfer.from_user.get_full_arabic_name }}</td>
                                <td>{{ transfer.transfer_date|date:"d/m/Y H:i" }}</td>
                                <td>
                                    {% if transfer.is_received %}
                                        <span class="badge bg-success">تم الاستلام</span>
                                    {% else %}
                                        <span class="badge bg-warning">معلق</span>
                                    {% endif %}
                                </td>
                                <td>{{ transfer.notes|default:"-" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- معلومات النظام -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>أدخل بواسطة:</strong></td>
                        <td>{{ document.created_by.get_full_arabic_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الإدخال:</strong></td>
                        <td>{{ document.created_at|date:"d/m/Y H:i" }}</td>
                    </tr>
                    <tr>
                        <td><strong>آخر تحديث:</strong></td>
                        <td>{{ document.updated_at|date:"d/m/Y H:i" }}</td>
                    </tr>
                    {% if document.assigned_to_user %}
                    <tr>
                        <td><strong>المحول إليه:</strong></td>
                        <td>{{ document.assigned_to_user.get_full_arabic_name }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>

        <!-- التعليقات -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-comments me-2"></i>
                    التعليقات
                </h5>
                <button class="btn btn-sm btn-primary" onclick="addComment()">
                    <i class="fas fa-plus"></i>
                </button>
            </div>
            <div class="card-body">
                {% if comments %}
                    {% for comment in comments %}
                    <div class="border-bottom pb-2 mb-2">
                        <div class="d-flex justify-content-between">
                            <strong>{{ comment.commented_by.get_full_arabic_name }}</strong>
                            <small class="text-muted">{{ comment.commented_at|date:"d/m/Y H:i" }}</small>
                        </div>
                        <p class="mb-1">{{ comment.comment }}</p>
                        {% if comment.is_internal %}
                            <small class="text-muted"><i class="fas fa-lock me-1"></i>تعليق داخلي</small>
                        {% endif %}
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد تعليقات</p>
                {% endif %}
            </div>
        </div>

        <!-- سجل الأحداث -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    سجل الأحداث
                </h5>
            </div>
            <div class="card-body">
                {% if actions %}
                    {% for action in actions %}
                    <div class="border-bottom pb-2 mb-2">
                        <div class="d-flex justify-content-between">
                            <strong>{{ action.get_action_type_display }}</strong>
                            <small class="text-muted">{{ action.performed_at|date:"d/m/Y H:i" }}</small>
                        </div>
                        <p class="mb-1">{{ action.description }}</p>
                        <small class="text-muted">{{ action.performed_by.get_full_arabic_name }}</small>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد أحداث</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function transferDocument() {
        alert('ميزة التحويل قيد التطوير');
    }
    
    function addComment() {
        alert('ميزة إضافة التعليقات قيد التطوير');
    }
    
    function updateStatus() {
        alert('ميزة تحديث الحالة قيد التطوير');
    }
    
    function printDocument() {
        window.print();
    }
    
    function viewFile(url) {
        window.open(url, '_blank');
    }
</script>
{% endblock %}

{% extends 'base.html' %}

{% block title %}الكتب الواردة - نظام إدارة الصادرة والواردة{% endblock %}

{% block content %}
<div class="page-title">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>الكتب الواردة</h1>
            <p>إدارة ومتابعة جميع الكتب الواردة</p>
        </div>
        <div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDocumentModal">
                <i class="fas fa-plus me-2"></i>
                إضافة كتاب وارد
            </button>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-search me-2"></i>
            البحث والتصفية
        </h6>
    </div>
    <div class="card-body">
        <form method="get" class="row g-2">
            <div class="col-md-4">
                <input type="text" class="form-control" id="search" name="search"
                       value="{{ search_query }}" placeholder="البحث في الكتب...">
            </div>
            <div class="col-md-2">
                <select class="form-select" id="status" name="status">
                    <option value="">كل الحالات</option>
                    {% for status in statuses %}
                        <option value="{{ status.pk }}" {% if status_filter == status.pk|stringformat:"s" %}selected{% endif %}>
                            {{ status.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="department" name="department">
                    <option value="">كل الأقسام</option>
                    {% for dept in departments %}
                        <option value="{{ dept.pk }}" {% if department_filter == dept.pk|stringformat:"s" %}selected{% endif %}>
                            {{ dept.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="type" name="type">
                    <option value="">كل الأنواع</option>
                    {% for doc_type in document_types %}
                        <option value="{{ doc_type.pk }}" {% if type_filter == doc_type.pk|stringformat:"s" %}selected{% endif %}>
                            {{ doc_type.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>
                    بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الكتب -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الكتب الواردة ({{ page_obj.paginator.count }} كتاب)
        </h6>
        <div class="d-flex gap-1">
            <button class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i>
            </button>
            <button class="btn btn-outline-danger btn-sm" onclick="exportToPDF()">
                <i class="fas fa-file-pdf"></i>
            </button>
        </div>
    </div>
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover table-sm">
                    <thead>
                        <tr>
                            <th>رقم الورود</th>
                            <th>الجهة المرسلة</th>
                            <th>الموضوع</th>
                            <th>الحالة</th>
                            <th>القسم</th>
                            <th>التاريخ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for document in page_obj %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ document.incoming_number }}</strong>
                                <br><small class="text-muted">{{ document.document_number }}</small>
                            </td>
                            <td>
                                {{ document.sender_entity|truncatechars:25 }}
                                <br><small class="text-muted">{{ document.document_type.name }}</small>
                            </td>
                            <td>{{ document.subject|truncatechars:35 }}</td>
                            <td>
                                <span class="badge" style="background-color: {{ document.status.color }}">
                                    {{ document.status.name }}
                                </span>
                                {% if document.priority == 'urgent' %}
                                    <br><span class="badge bg-danger mt-1">عاجل جداً</span>
                                {% elif document.priority == 'high' %}
                                    <br><span class="badge bg-warning mt-1">عاجل</span>
                                {% endif %}
                            </td>
                            <td>{{ document.assigned_to_department.name }}</td>
                            <td>{{ document.incoming_date|date:"d/m/Y" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{% url 'documents:incoming_detail' document.pk %}"
                                       class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-outline-success"
                                            onclick="transferDocument({{ document.pk }})" title="تحويل">
                                        <i class="fas fa-share"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- التصفح -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="تصفح الصفحات">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد كتب واردة</h4>
                <p class="text-muted">لم يتم العثور على أي كتب واردة تطابق معايير البحث</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDocumentModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول كتاب وارد
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal إضافة كتاب وارد -->
<div class="modal fade" id="addDocumentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة كتاب وارد جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-center text-muted">
                    <i class="fas fa-tools fa-2x mb-2"></i><br>
                    هذه الميزة قيد التطوير
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function transferDocument(documentId) {
        alert('ميزة التحويل قيد التطوير');
    }
    
    function addComment(documentId) {
        alert('ميزة إضافة التعليقات قيد التطوير');
    }
    
    function exportToExcel() {
        alert('ميزة التصدير إلى Excel قيد التطوير');
    }
    
    function exportToPDF() {
        alert('ميزة التصدير إلى PDF قيد التطوير');
    }
</script>
{% endblock %}

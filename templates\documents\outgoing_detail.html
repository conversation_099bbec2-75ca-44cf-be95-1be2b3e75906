{% extends 'base.html' %}

{% block title %}تفاصيل الكتاب الصادر {{ document.document_number }} - نظام إدارة الصادرة والواردة{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'documents:outgoing_list' %}">الكتب الصادرة</a></li>
        <li class="breadcrumb-item active">{{ document.document_number }}</li>
    </ol>
</nav>

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>تفاصيل الكتاب الصادر</h1>
            <p>رقم الكتاب: {{ document.document_number }}</p>
        </div>
        <div>
            <div class="btn-group" role="group">
                <button class="btn btn-success" onclick="sendDocument()">
                    <i class="fas fa-paper-plane me-2"></i>
                    إرسال
                </button>
                <button class="btn btn-info" onclick="addComment()">
                    <i class="fas fa-comment me-2"></i>
                    إضافة تعليق
                </button>
                <button class="btn btn-warning" onclick="updateStatus()">
                    <i class="fas fa-edit me-2"></i>
                    تحديث الحالة
                </button>
                <button class="btn btn-outline-primary" onclick="printDocument()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- معلومات الكتاب الأساسية -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    معلومات الكتاب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم الكتاب:</strong></td>
                                <td>{{ document.document_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الكتاب:</strong></td>
                                <td>{{ document.document_date|date:"d/m/Y" }}</td>
                            </tr>
                            <tr>
                                <td><strong>الجهة المرسل إليها:</strong></td>
                                <td>{{ document.recipient_entity }}</td>
                            </tr>
                            {% if document.recipient_address %}
                            <tr>
                                <td><strong>عنوان الجهة:</strong></td>
                                <td>{{ document.recipient_address }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>القسم المرسل:</strong></td>
                                <td>{{ document.department.name }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>نوع الكتاب:</strong></td>
                                <td><span class="badge bg-info">{{ document.document_type.name }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <span class="badge" style="background-color: {{ document.status.color }}">
                                        {{ document.status.name }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الأولوية:</strong></td>
                                <td>
                                    {% if document.priority == 'urgent' %}
                                        <span class="badge bg-danger">عاجل جداً</span>
                                    {% elif document.priority == 'high' %}
                                        <span class="badge bg-warning">عاجل</span>
                                    {% elif document.priority == 'medium' %}
                                        <span class="badge bg-primary">مهم</span>
                                    {% else %}
                                        <span class="badge bg-secondary">عادي</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>أعد بواسطة:</strong></td>
                                <td>{{ document.prepared_by.get_full_arabic_name }}</td>
                            </tr>
                            {% if document.delivery_method %}
                            <tr>
                                <td><strong>طريقة التسليم:</strong></td>
                                <td>{{ document.get_delivery_method_display }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6><strong>موضوع الكتاب:</strong></h6>
                        <p class="bg-light p-3 rounded">{{ document.subject }}</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <h6><strong>نص الكتاب:</strong></h6>
                        <div class="bg-light p-3 rounded" style="white-space: pre-wrap;">{{ document.content }}</div>
                    </div>
                </div>
                
                {% if document.notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6><strong>ملاحظات:</strong></h6>
                        <p class="bg-light p-3 rounded">{{ document.notes }}</p>
                    </div>
                </div>
                {% endif %}
                
                {% if document.sent_date %}
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>تم الإرسال في:</strong> {{ document.sent_date|date:"d/m/Y H:i" }}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- الملف المرفق -->
        {% if document.document_file %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-paperclip me-2"></i>
                    الملف المرفق
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <i class="fas fa-file fa-2x text-primary me-3"></i>
                    <div>
                        <h6 class="mb-1">{{ document.document_file.name|slice:"20:" }}</h6>
                        <small class="text-muted">حجم الملف: {{ document.document_file.size|filesizeformat }}</small>
                    </div>
                    <div class="ms-auto">
                        <a href="{{ document.document_file.url }}" class="btn btn-primary" target="_blank">
                            <i class="fas fa-download me-1"></i>
                            تحميل
                        </a>
                        <button class="btn btn-outline-primary" onclick="viewFile('{{ document.document_file.url }}')">
                            <i class="fas fa-eye me-1"></i>
                            عرض
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- المسؤولون -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    المسؤولون
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-user-edit fa-2x text-primary mb-2"></i>
                            <h6>أعد بواسطة</h6>
                            <p class="mb-0">{{ document.prepared_by.get_full_arabic_name }}</p>
                            <small class="text-muted">{{ document.prepared_by.position|default:"" }}</small>
                        </div>
                    </div>
                    {% if document.approved_by %}
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                            <h6>وافق عليه</h6>
                            <p class="mb-0">{{ document.approved_by.get_full_arabic_name }}</p>
                            <small class="text-muted">{{ document.approved_by.position|default:"" }}</small>
                        </div>
                    </div>
                    {% endif %}
                    {% if document.signed_by %}
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-signature fa-2x text-warning mb-2"></i>
                            <h6>وقع بواسطة</h6>
                            <p class="mb-0">{{ document.signed_by.get_full_arabic_name }}</p>
                            <small class="text-muted">{{ document.signed_by.position|default:"" }}</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- معلومات النظام -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>تاريخ الإنشاء:</strong></td>
                        <td>{{ document.created_at|date:"d/m/Y H:i" }}</td>
                    </tr>
                    <tr>
                        <td><strong>آخر تحديث:</strong></td>
                        <td>{{ document.updated_at|date:"d/m/Y H:i" }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- التعليقات -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-comments me-2"></i>
                    التعليقات
                </h5>
                <button class="btn btn-sm btn-primary" onclick="addComment()">
                    <i class="fas fa-plus"></i>
                </button>
            </div>
            <div class="card-body">
                {% if comments %}
                    {% for comment in comments %}
                    <div class="border-bottom pb-2 mb-2">
                        <div class="d-flex justify-content-between">
                            <strong>{{ comment.commented_by.get_full_arabic_name }}</strong>
                            <small class="text-muted">{{ comment.commented_at|date:"d/m/Y H:i" }}</small>
                        </div>
                        <p class="mb-1">{{ comment.comment }}</p>
                        {% if comment.is_internal %}
                            <small class="text-muted"><i class="fas fa-lock me-1"></i>تعليق داخلي</small>
                        {% endif %}
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد تعليقات</p>
                {% endif %}
            </div>
        </div>

        <!-- سجل الأحداث -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    سجل الأحداث
                </h5>
            </div>
            <div class="card-body">
                {% if actions %}
                    {% for action in actions %}
                    <div class="border-bottom pb-2 mb-2">
                        <div class="d-flex justify-content-between">
                            <strong>{{ action.get_action_type_display }}</strong>
                            <small class="text-muted">{{ action.performed_at|date:"d/m/Y H:i" }}</small>
                        </div>
                        <p class="mb-1">{{ action.description }}</p>
                        <small class="text-muted">{{ action.performed_by.get_full_arabic_name }}</small>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد أحداث</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function sendDocument() {
        if (confirm('هل أنت متأكد من إرسال هذا الكتاب؟')) {
            alert('ميزة الإرسال قيد التطوير');
        }
    }
    
    function addComment() {
        alert('ميزة إضافة التعليقات قيد التطوير');
    }
    
    function updateStatus() {
        alert('ميزة تحديث الحالة قيد التطوير');
    }
    
    function printDocument() {
        window.print();
    }
    
    function viewFile(url) {
        window.open(url, '_blank');
    }
</script>
{% endblock %}

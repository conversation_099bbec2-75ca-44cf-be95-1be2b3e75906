{% extends 'base.html' %}

{% block title %}الكتب الصادرة - نظام إدارة الصادرة والواردة{% endblock %}

{% block content %}
<div class="page-title">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>الكتب الصادرة</h1>
            <p>إدارة ومتابعة جميع الكتب الصادرة</p>
        </div>
        <div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDocumentModal">
                <i class="fas fa-plus me-2"></i>
                إنشاء كتاب صادر
            </button>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            البحث والتصفية
        </h5>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search_query }}" placeholder="رقم الكتاب، الجهة المرسل إليها، الموضوع...">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    {% for status in statuses %}
                        <option value="{{ status.pk }}" {% if status_filter == status.pk|stringformat:"s" %}selected{% endif %}>
                            {{ status.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="department" class="form-label">القسم</label>
                <select class="form-select" id="department" name="department">
                    <option value="">جميع الأقسام</option>
                    {% for dept in departments %}
                        <option value="{{ dept.pk }}" {% if department_filter == dept.pk|stringformat:"s" %}selected{% endif %}>
                            {{ dept.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="type" class="form-label">النوع</label>
                <select class="form-select" id="type" name="type">
                    <option value="">جميع الأنواع</option>
                    {% for doc_type in document_types %}
                        <option value="{{ doc_type.pk }}" {% if type_filter == doc_type.pk|stringformat:"s" %}selected{% endif %}>
                            {{ doc_type.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{% url 'documents:outgoing_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الكتب -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الكتب الصادرة
        </h5>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary btn-sm" onclick="exportToExcel()">
                <i class="fas fa-file-excel me-1"></i>
                تصدير Excel
            </button>
            <button class="btn btn-outline-danger btn-sm" onclick="exportToPDF()">
                <i class="fas fa-file-pdf me-1"></i>
                تصدير PDF
            </button>
        </div>
    </div>
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الكتاب</th>
                            <th>الجهة المرسل إليها</th>
                            <th>الموضوع</th>
                            <th>النوع</th>
                            <th>الحالة</th>
                            <th>الأولوية</th>
                            <th>القسم المرسل</th>
                            <th>تاريخ الكتاب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for document in page_obj %}
                        <tr>
                            <td>
                                <strong>{{ document.document_number }}</strong>
                            </td>
                            <td>{{ document.recipient_entity|truncatechars:30 }}</td>
                            <td>{{ document.subject|truncatechars:40 }}</td>
                            <td>
                                <span class="badge bg-info">{{ document.document_type.name }}</span>
                            </td>
                            <td>
                                <span class="badge" style="background-color: {{ document.status.color }}">
                                    {{ document.status.name }}
                                </span>
                            </td>
                            <td>
                                {% if document.priority == 'urgent' %}
                                    <span class="badge bg-danger">عاجل جداً</span>
                                {% elif document.priority == 'high' %}
                                    <span class="badge bg-warning">عاجل</span>
                                {% elif document.priority == 'medium' %}
                                    <span class="badge bg-primary">مهم</span>
                                {% else %}
                                    <span class="badge bg-secondary">عادي</span>
                                {% endif %}
                            </td>
                            <td>{{ document.department.name }}</td>
                            <td>{{ document.document_date|date:"d/m/Y" }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'documents:outgoing_detail' document.pk %}" 
                                       class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-success" 
                                            onclick="sendDocument({{ document.pk }})" title="إرسال">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" 
                                            onclick="addComment({{ document.pk }})" title="إضافة تعليق">
                                        <i class="fas fa-comment"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- التصفح -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="تصفح الصفحات">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-paper-plane fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد كتب صادرة</h4>
                <p class="text-muted">لم يتم العثور على أي كتب صادرة تطابق معايير البحث</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDocumentModal">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء أول كتاب صادر
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal إنشاء كتاب صادر -->
<div class="modal fade" id="addDocumentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء كتاب صادر جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-center text-muted">
                    <i class="fas fa-tools fa-2x mb-2"></i><br>
                    هذه الميزة قيد التطوير
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function sendDocument(documentId) {
        alert('ميزة الإرسال قيد التطوير');
    }
    
    function addComment(documentId) {
        alert('ميزة إضافة التعليقات قيد التطوير');
    }
    
    function exportToExcel() {
        alert('ميزة التصدير إلى Excel قيد التطوير');
    }
    
    function exportToPDF() {
        alert('ميزة التصدير إلى PDF قيد التطوير');
    }
</script>
{% endblock %}

Metadata-Version: 2.4
Name: django-extensions
Version: 4.1
Summary: Extensions for Django
Author-email: <PERSON> <<EMAIL>>
Maintainer-email: <PERSON><PERSON> <<EMAIL>>
License-Expression: MIT
Project-URL: Changelog, https://github.com/django-extensions/django-extensions/blob/main/CHANGELOG.md
Project-URL: Documentation, https://django-extensions.readthedocs.io/
Project-URL: Homepage, https://github.com/django-extensions/django-extensions
Project-URL: Source, https://github.com/django-extensions/django-extensions
Project-URL: Tracker, https://github.com/django-extensions/django-extensions/issues
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Framework :: Django
Classifier: Framework :: Django :: 4.2
Classifier: Framework :: Django :: 5.1
Classifier: Framework :: Django :: 5.2
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Utilities
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: django>=4.2
Dynamic: license-file

===================
 Django Extensions
===================

.. image:: https://img.shields.io/pypi/l/django-extensions.svg
   :target: https://raw.githubusercontent.com/django-extensions/django-extensions/master/LICENSE

.. image:: https://github.com/django-extensions/django-extensions/actions/workflows/compile_catalog.yml/badge.svg
    :target: https://github.com/django-extensions/django-extensions/actions

.. image:: https://github.com/django-extensions/django-extensions/actions/workflows/linters.yml/badge.svg
    :target: https://github.com/django-extensions/django-extensions/actions

.. image:: https://github.com/django-extensions/django-extensions/actions/workflows/precommit.yml/badge.svg
    :target: https://github.com/django-extensions/django-extensions/actions

.. image:: https://github.com/django-extensions/django-extensions/actions/workflows/pytest.yml/badge.svg
    :target: https://github.com/django-extensions/django-extensions/actions

.. image:: https://github.com/django-extensions/django-extensions/actions/workflows/security.yml/badge.svg
    :target: https://github.com/django-extensions/django-extensions/actions

.. image:: https://img.shields.io/pypi/v/django-extensions.svg
    :target: https://pypi.python.org/pypi/django-extensions/
    :alt: Latest PyPI version

.. image:: https://img.shields.io/pypi/wheel/django-extensions.svg
    :target: https://pypi.python.org/pypi/django-extensions/
    :alt: Supports Wheel format

.. image:: https://coveralls.io/repos/django-extensions/django-extensions/badge.svg?branch=master
   :target: https://coveralls.io/r/django-extensions/django-extensions?branch=master
   :alt: Coverage

Django Extensions is a collection of custom extensions for the Django Framework.


Getting Started
===============

The easiest way to figure out what Django Extensions are all about is to watch the
`excellent screencast by Eric Holscher`__ (`watch the video on vimeo`__). In a couple
minutes Eric walks you through a half a dozen command extensions. There is also a
`short screencast on GoDjango's Youtube Channel`__ to help show you even more.


Requirements
============

Django Extensions requires Django 4.2 or later.


Getting It
==========

You can get Django Extensions by using pip::

    $ pip install django-extensions

If you want to install it from source, grab the git repository from GitHub::

    $ git clone git://github.com/django-extensions/django-extensions.git
    $ cd django-extensions
    $ pip install .


Installing It
=============

To enable `django_extensions` in your project you need to add it to `INSTALLED_APPS` in your projects
`settings.py` file:

.. code-block:: python

    INSTALLED_APPS = (
        ...
        'django_extensions',
        ...
    )


Using It
========

Generate (and view) a graphviz graph of app models::

    $ python manage.py graph_models -a -o myapp_models.png

Produce a tab-separated list of `(url_pattern, view_function, name)` tuples for a project::

    $ python manage.py show_urls

Check templates for rendering errors::

    $ python manage.py validate_templates

Run the enhanced django shell::

    $ python manage.py shell_plus

Run the enhanced django runserver, (requires Werkzeug install)::

    $ python manage.py runserver_plus


Getting Involved
================

Open Source projects can always use more help. Fixing a problem, documenting a feature, adding
translation in your language. If you have some time to spare and like to help us, here are the places to do so:

- GitHub: https://github.com/django-extensions/django-extensions
- Mailing list: https://groups.google.com/group/django-extensions
- Translations: https://www.transifex.com/projects/p/django-extensions/


Documentation
=============

You can view documentation online at:

- https://django-extensions.readthedocs.io

Or you can look at the docs/ directory in the repository.


Support
=======

Django Extensions is free and always will be. It is developed and maintained by developers in an Open Source manner.
Any support is welcome. You could help by writing documentation, pull-requests, report issues and/or translations.

Please remember that nobody is paid directly to develop or maintain Django Extensions so we do have to divide our time
between putting food on the table, family, this project and the rest of life :-)


__ https://ericholscher.com/blog/2008/sep/12/screencast-django-command-extensions/
__ https://vimeo.com/1720508
__ https://www.youtube.com/watch?v=1F6G3ONhr4k

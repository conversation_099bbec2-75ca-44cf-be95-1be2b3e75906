# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-02-10 20:37+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: admin/__init__.py:142
msgid "and"
msgstr ""

#: admin/__init__.py:144
#, python-format
msgid ""
"Use the left field to do %(model_name)s lookups in the fields %(field_list)s."
msgstr ""

#: admin/filter.py:24 admin/filter.py:53
msgid "Yes"
msgstr ""

#: admin/filter.py:25 admin/filter.py:54
msgid "No"
msgstr ""

#: admin/filter.py:32
msgid "All"
msgstr ""

#: db/models.py:18
msgid "created"
msgstr ""

#: db/models.py:19
msgid "modified"
msgstr ""

#: db/models.py:38
msgid "title"
msgstr ""

#: db/models.py:39
msgid "description"
msgstr ""

#: db/models.py:60
msgid "slug"
msgstr ""

#: db/models.py:121 mongodb/models.py:76
msgid "Inactive"
msgstr ""

#: db/models.py:122 mongodb/models.py:77
msgid "Active"
msgstr ""

#: db/models.py:124
msgid "status"
msgstr ""

#: db/models.py:125 mongodb/models.py:80
msgid "keep empty for an immediate activation"
msgstr ""

#: db/models.py:126 mongodb/models.py:81
msgid "keep empty for indefinite activation"
msgstr ""

#: mongodb/fields/__init__.py:22
#, python-format
msgid "String (up to %(max_length)s)"
msgstr ""

#: validators.py:14
msgid "Control Characters like new lines or tabs are not allowed."
msgstr ""

#: validators.py:48
msgid "Leading and Trailing whitespaces are not allowed."
msgstr ""

#: validators.py:74
msgid "Only a hex string is allowed."
msgstr ""

#: validators.py:75
#, python-format
msgid "Invalid length. Must be %(length)d characters."
msgstr ""

#: validators.py:76
#, python-format
msgid "Ensure that there are more than %(min)s characters."
msgstr ""

#: validators.py:77
#, python-format
msgid "Ensure that there are no more than %(max)s characters."
msgstr ""
